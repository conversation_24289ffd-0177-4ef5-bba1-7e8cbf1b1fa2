import { useEffect, useState, use<PERSON><PERSON>back, useMemo, useRef, useContext } from "react"
import Tabs from "@mui/material/Tabs"
import Tab from "@mui/material/Tab"
import styles from "./Data-mapping.module.css"
import globalStyles from "../../globalStyles.module.css"
import { CheckIcon, SearchIcon } from "@heroicons/react/solid"
import { HiNoSymbol } from "react-icons/hi2"
import { HiOutlineFunnel } from "react-icons/hi2"
import Dropdown from "../Dropdown/Dropdown"
import ValueMapping from "./Value-mapping/Value-mapping"
import DefaultValue from "./Default-value/DefaultValue"
import CombineFields from "./Combine-value/CombineFields"
import OverrideValues from "./OverrideValues/OverrideValues"
import Advanced from "./Advanced/Advanced"
import { postActivity, saveMigrationPlan } from "../../apiService"
import { CogIcon } from "@heroicons/react/outline"
import { MigrationContext } from "../Data-Migration"
import { toast, ToastContainer } from "react-toastify"
import { Dialog, Dialog<PERSON>ontent, Form<PERSON>ontrol<PERSON>abel, Switch } from "@mui/material"
import "react-toastify/dist/ReactToastify.css"
import { displayArticle } from "../../../Helper/helper"
import LoaderSpinner from "../../loaderspinner"

export default function DataMapping({ setSelectedTab, planId, setPlanId, templateName }) {
  const { migrationState, setMigrationState } = useContext(MigrationContext)
  const dataTypeData = migrationState.dataTypeData
  const source = migrationState.sourceObjData
  const target = migrationState.targetData
  const dataMappingData = migrationState.dataMappingData
  const targetExeRes = migrationState.targetExeRes
  const selectedObjectData = migrationState.selectedObjectData
  const selectedEntityData = migrationState.selectedEntityData
  const sourceExeRes = migrationState.sourceExeRes
  const sourceMapRes = migrationState.sourceMapRes
  const sourceResAtt = migrationState.sourceResAtt
  const dependentFields = migrationState.dataTypeData.dependentFields

  const [email, setEmail] = useState(localStorage.getItem("email"))
  const settingsDropdownRef = useRef({})
  const filterDropdownRef = useRef(null)
  const [value, setValue] = useState(0)
  const [isDependencyAdded, setIsDependencyAdded] = useState(true)
  const [mappedSourceFields, setMappedSourceFields] = useState(new Set())
  const [openDialog, setOpenDialog] = useState(false)
  const [openDefaultDialog, setOpenDefaultDialog] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const showPages = [15, 25, 50]
  const [itemsPerPage, setItemsPerPage] = useState(showPages[0])
  const [attributeNames, setAttributeNames] = useState([])
  const [filteredAttributes, setFilteredAttributes] = useState([])
  const [filteredList, setFilteredList] = useState([])
  const [targets, setTargets] = useState([])
  const [targetData, setTargetData] = useState([])
  const [searchTerm, setSearchTerm] = useState("")
  const [shouldRefreshTargets, setShouldRefreshTargets] = useState(false)
  const [currentTabData, setCurrentTabData] = useState([])
  const [selectedMapping, setSelectedMapping] = useState(null)
  const [selectedTarget, setSelectedTarget] = useState(null)
  const [showSettingsDropdown, setShowSettingsDropdown] = useState({})
  const [activeSettingIndex, setActiveSettingIndex] = useState(null)
  const [openCombineFieldsDialog, setOpenCombineFieldsDialog] = useState(false)
  const [openOverrideDialog, setOpenOverrideDialog] = useState(false)
  const [openAdvancedDialog, setOpenAdvancedDialog] = useState(false)
  const [isNotes, setIsNotes] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [savingMessage, setSavingMessage] = useState("Saving...")

  const [showFilterDropdown, setShowFilterDropdown] = useState(false)
  const [selectedFilter, setSelectedFilter] = useState("See all")
  const allFilterOptions = [
    "See all",
    "Mapped successfully",
    "Errors in mapping",
    "Data mismatch",
    "Unmapped fields",
    "Skipped fields",
    "Required fields",
  ]

  // Function to get data type icon
  const getDataTypeIcon = useCallback((type) => {
    const iconMap = {
      string: "/assets/text.png",
      number: "/assets/number.png",
      array: "/assets/array.png",
      "array of objects": "/assets/array.png",
      datetime: "/assets/date.png",
      date: "/assets/date.png",
      boolean: "/assets/text.png",
    }
    return iconMap[type] || "/assets/text.png"
  }, [])

  const getFilterOptions = () => {
    if (selectedFilter === "See all") {
      return allFilterOptions.filter((option) => option !== "See all")
    }
    return allFilterOptions.filter((option) => option !== selectedFilter)
  }

  const getSourceFieldDropdownValuesforFields = (attribute) => {
    if (source.uniqueSourceValues && attribute.mappingEnabled) {
      return getValuesByColumnNameforFields(source.uniqueSourceValues, attribute.sourcefield, "column", "values")
    }
    return []
  }

  const getValuesByColumnNameforFields = (arr, columnName, searchFieldName, returnFieldName) => {
    if (arr) {
      const columnObject = arr.find((obj) => obj[searchFieldName] === columnName)
      return columnObject ? columnObject[returnFieldName] : []
    }
    return []
  }

  const getSourceMappingObjectKey = (sourceMappingConfig) => {
    return sourceMappingConfig?.sourceMappingObjectKey?.trim()
      ? sourceMappingConfig.sourceMappingObjectKey
      : "ticket_fields"
  }

  const getSourceMappingFieldName = (sourceMappingConfig) => {
    return sourceMappingConfig?.fieldName?.trim() ? sourceMappingConfig.fieldName : "title"
  }

  const getTotalSourceCountforFields = (attribute, target) => {
    const sourceMappingKey = getSourceMappingObjectKey(
      selectedObjectData?.sourceMappingConfig ? selectedObjectData.sourceMappingConfig : "",
    )
    const fieldName = getSourceMappingFieldName(
      selectedObjectData?.sourceMappingConfig ? selectedObjectData.sourceMappingConfig : "",
    )

    if (attribute) {
      if (source.type == "csv") {
        return getSourceFieldDropdownValuesforFields(attribute).length
      } else if (source.type == "api") {
        if (target.name !== selectedEntityData.mainTarget[0].name && sourceMapRes && sourceMapRes[target.name]) {
          return sourceMapRes[target.name].length
        } else if (
          target.name == selectedEntityData?.mainTarget[0].name &&
          (attribute.sourcefield == "sub_category" ||
            attribute.sourcefield == "category" ||
            attribute.attribute == "sub_category" ||
            attribute.sourcefield == "item_category" ||
            attribute.attribute == "item_category")
        ) {
          if (attribute?.mappings?.length > 0) {
            const totalSourceValueLength = attribute?.mappings?.reduce((total, item) => {
              const validCount = Array.isArray(item?.sourcevalue)
                ? item?.sourcevalue.filter((value) => value !== "" && value !== null).length
                : 0

              return total + validCount
            }, 0)

            return totalSourceValueLength
          } else {
            return -1
          }
        } else {
          if (sourceExeRes && sourceExeRes[sourceMappingKey] && sourceExeRes[sourceMappingKey].length > 0) {
            for (const field of sourceExeRes[sourceMappingKey]) {
              if (attribute.sourcefield == field[fieldName]) {
                return field.choices.length
              }
            }
          }
        }
      }
    }
    return 0
  }

  const getTotalMappedCountforFields = (attribute) => {
    if (attribute && attribute.mappings) {
      const filteredMappings = attribute.mappings.filter(
        (obj) =>
          Array.isArray(obj.sourcevalue) &&
          obj.sourcevalue.some((value) => value !== "" && value !== null && value !== "null"),
      )
      const totalSourceValues = filteredMappings.reduce((acc, obj) => {
        const validValues = obj.sourcevalue.filter((value) => value !== "" && value !== null && value !== "null")
        return acc + validValues.length
      }, 0)
      return totalSourceValues
    }
    return 0
  }

  const showAlertIcon = (attribute, target, emptyColumns = []) => {
    if (attribute && !attribute.skip) {
      if (
        attribute.required &&
        (!attribute.sourcefield || attribute.sourcefield === "" || attribute.sourcefield === undefined) &&
        (attribute.default === undefined || attribute.default === "") &&
        (!attribute.combinedFields || (attribute.combinedFields && attribute.combinedFields.length == 0))
      ) {
        return true
      }
      if (source.type == "csv" && emptyColumns.includes(attribute.sourcefield)) {
        return true
      } else if (attribute.mappingEnabled) {
        if (
          target.name == selectedEntityData.mainTarget[0].name &&
          (attribute.targetfield == "category" ||
            attribute.targetfield == "sub_category" ||
            attribute.targetfield == "item_category")
        ) {
          if (!attribute.mappings || attribute.mappings.length === 0) {
            return true
          }

          const hasEmptySourceValues = attribute.mappings.some(
            (mapping) =>
              !mapping.sourcevalue ||
              mapping.sourcevalue.length === 0 ||
              mapping.sourcevalue.every((val) => val === "" || val === null || val === "null"),
          )

          if (hasEmptySourceValues) {
            return true
          }

          if (getTotalSourceCountforFields(attribute, target) - getTotalMappedCountforFields(attribute) != 0) {
            return true
          }
        } else if (getTotalSourceCountforFields(attribute, target) - getTotalMappedCountforFields(attribute) != 0) {
          if (target.name == "Requesters" && attribute.targetfield == "first_name") {
            return false
          }
          return true
        }
      } else if (
        (!attribute.sourcefield || attribute.sourcefield === "" || attribute.sourcefield === undefined) &&
        (attribute.default === undefined || attribute.default === "") &&
        (!attribute.combinedFields || (attribute.combinedFields && attribute.combinedFields.length == 0))
      ) {
        return true
      }
    }
    return false
  }

  const handleAdvancedMappingDialog = (mapping, target) => {
    setOpenAdvancedDialog(true)
    setSelectedMapping(mapping)
    setSelectedTarget(target)
    setShowSettingsDropdown({})
  }

  const handleSaveAdvancedCode = (code) => {
    if (selectedMapping && selectedTarget) {
      const newTargets = [...targets]
      const targetIndex = newTargets.findIndex((t) => t.name === selectedTarget.name)

      if (targetIndex !== -1) {
        const fieldIndex = newTargets[targetIndex].fieldMappings.findIndex(
          (f) => f.targetfield === selectedMapping.targetfield,
        )

        if (fieldIndex !== -1) {
          newTargets[targetIndex].fieldMappings[fieldIndex]["mappingType"] = "js"
          newTargets[targetIndex].fieldMappings[fieldIndex]["value"] = code

          setTargets(newTargets)
          setTargetData(newTargets)
        }
      }
    }
  }

  const handleOverrideValuesDialog = (mapping, target) => {
    setOpenOverrideDialog(true)
    setSelectedMapping(mapping)
    setSelectedTarget(target)
    setShowSettingsDropdown({})
  }

  const handleSaveOverrideValues = (overrideMappings) => {
    if (selectedMapping && selectedTarget) {
      const newTargets = [...targets]
      const targetIndex = newTargets.findIndex((t) => t.name === selectedTarget.name)

      if (targetIndex !== -1) {
        const fieldIndex = newTargets[targetIndex].fieldMappings.findIndex(
          (f) => f.targetfield === selectedMapping.targetfield,
        )

        if (fieldIndex !== -1) {
          newTargets[targetIndex].fieldMappings[fieldIndex].override = overrideMappings
          setTargets(newTargets)
          setTargetData(newTargets)
        }
      }
    }
  }

  const handleCombineFieldsDialog = (mapping, target) => {
    setOpenCombineFieldsDialog(true)
    setSelectedMapping(mapping)
    setSelectedTarget(target)
    setShowSettingsDropdown({})
  }

  const handleSaveCombinedFields = (combinedFields) => {
    if (selectedMapping && selectedTarget && combinedFields.length >= 2) {
      const newTargets = [...targets]
      const targetIndex = newTargets.findIndex((t) => t.name === selectedTarget.name)

      if (targetIndex !== -1) {
        const fieldIndex = newTargets[targetIndex].fieldMappings.findIndex(
          (f) => f.targetfield === selectedMapping.targetfield,
        )

        if (fieldIndex !== -1) {
          newTargets[targetIndex].fieldMappings[fieldIndex].combinedFields = combinedFields
          setTargets(newTargets)
          setTargetData(newTargets)
        }
      }
    }
  }

  const handleValueMappingSave = (mapping, attribute, name) => {
    console.log(mapping)
    const updatedTargets = JSON.parse(JSON.stringify(targets))
    const targetIndex = updatedTargets.findIndex((t) => t.name === name)

    if (targetIndex !== -1) {
      const fieldIndex = updatedTargets[targetIndex].fieldMappings.findIndex(
        (field) => field.targetfield === attribute.targetfield,
      )

      if (fieldIndex !== -1) {
        if (!updatedTargets[targetIndex].fieldMappings[fieldIndex].mappings) {
          updatedTargets[targetIndex].fieldMappings[fieldIndex].mappings = []
        }

        updatedTargets[targetIndex].fieldMappings[fieldIndex].mappings = mapping
        const hasValidMappings = mapping.some(
          (m) =>
            Array.isArray(m.sourcevalue) && m.sourcevalue.some((val) => val !== "" && val !== null && val !== "null"),
        )

        updatedTargets[targetIndex].fieldMappings[fieldIndex].status = hasValidMappings ? "success" : "error"

        const target = updatedTargets[targetIndex]
        if (target.name === "Tickets" && attribute.targetfield === "category") {
          const subcategoryMappings = []
          const itemcategoryMappings = []

          for (const item of mapping) {
            if (item.subCategories && item.subCategories.length > 0) {
              for (const sub of item.subCategories) {
                if (!Array.isArray(sub.sourcevalue)) {
                  sub.sourcevalue = sub.sourcevalue ? [sub.sourcevalue] : []
                }
                subcategoryMappings.push(sub)

                if (sub.items && sub.items.length > 0) {
                  for (const itm of sub.items) {
                    if (!Array.isArray(itm.sourcevalue)) {
                      itm.sourcevalue = itm.sourcevalue ? [itm.sourcevalue] : []
                    }
                    itemcategoryMappings.push(itm)
                  }
                }
              }
            }
          }

          for (const field of target.fieldMappings) {
            if (field.targetfield === "sub_category" && subcategoryMappings.length > 0) {
              field.mappings = subcategoryMappings
              const hasValidSubcategoryMappings = subcategoryMappings.some(
                (m) =>
                  Array.isArray(m.sourcevalue) &&
                  m.sourcevalue.some((val) => val !== "" && val !== null && val !== "null"),
              )
              field.status = hasValidSubcategoryMappings ? "success" : "error"
            }
            if (field.targetfield === "item_category" && itemcategoryMappings.length > 0) {
              field.mappings = itemcategoryMappings
              const hasValidItemMappings = itemcategoryMappings.some(
                (m) =>
                  Array.isArray(m.sourcevalue) &&
                  m.sourcevalue.some((val) => val !== "" && val !== null && val !== "null"),
              )
              field.status = hasValidItemMappings ? "success" : "error"
            }
          }
        }

        setTargets(updatedTargets)
        setTargetData([...updatedTargets])
      }
    }
  }

  useEffect(() => {
    console.log(targetData)
  }, [targetData])

  const handleDefaultMappingDialog = (mapping, target) => {
    setOpenDefaultDialog(true)
    setSelectedMapping(mapping)
    setSelectedTarget(target)
    setShowSettingsDropdown({})
  }

  const handleSaveDefaultValue = (defaultValue) => {
    if (selectedMapping && selectedTarget) {
      const newTargets = [...targets]
      const targetIndex = newTargets.findIndex((t) => t.name === selectedTarget.name)

      if (targetIndex !== -1) {
        const fieldIndex = newTargets[targetIndex].fieldMappings.findIndex(
          (f) => f.targetfield === selectedMapping.targetfield,
        )

        if (fieldIndex !== -1) {
          newTargets[targetIndex].fieldMappings[fieldIndex].default = defaultValue
          if (newTargets[targetIndex].fieldMappings[fieldIndex].value !== undefined) {
            newTargets[targetIndex].fieldMappings[fieldIndex].value = defaultValue
          } else {
            newTargets[targetIndex].fieldMappings[fieldIndex]["value"] = defaultValue
          }
          setTargets(newTargets)
          setTargetData(newTargets)
        }
      }
    }
  }

  useEffect(() => {
    function handleClickOutside(event) {
      if (!Object.values(settingsDropdownRef.current || {}).some((ref) => ref?.contains(event.target))) {
        setShowSettingsDropdown({})
      }
      if (filterDropdownRef.current && !filterDropdownRef.current.contains(event.target)) {
        setShowFilterDropdown(false)
      }
    }
    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [])

  const handleChange = (event, newValue) => {
    setValue(newValue)
    setCurrentPage(1)

    if (targetData[newValue]?.fieldMappings) {
      setCurrentTabData(targetData[newValue].fieldMappings)
    }
  }

  const initialTargets = useMemo(() => {
    if (!targetExeRes || !sourceResAtt) return []

    const mappingTargets = selectedEntityData?.mappingTargets || []
    const mainTarget = selectedEntityData?.mainTarget || []
    const dependentTargets = JSON.parse(
      JSON.stringify(
        selectedEntityData?.dependentTargets?.filter((target) => dependentFields.includes(target.name)) || [],
      ),
    )

    return [...mappingTargets, ...mainTarget, ...dependentTargets]
  }, [targetExeRes, sourceResAtt, dependentFields, selectedEntityData])

  useEffect(() => {
    if (dataMappingData.targets) {
      setTargets(dataMappingData.targets)
    } else {
      setTargets(initialTargets)
    }

    if (source.type === "api") {
      setFilteredAttributes(sourceResAtt.filter((val) => val !== "").sort())
    } else {
      setFilteredAttributes(source.uniqueSourceValues?.map((val) => val.column))
    }
  }, [initialTargets, sourceResAtt, dataMappingData])

  useEffect(() => {
    if (dataMappingData.isNotes) {
      setIsNotes(dataMappingData.isNotes)
    }
  }, [dataMappingData])

  useEffect(() => {
    setTargetData(targets)
  }, [targets])

  const isRequired = useCallback(
    (targetfield) => {
      if (targetExeRes && targetExeRes.ticketFields) {
        return targetExeRes.ticketFields.some((field) => field.name === targetfield && field.required_for_agents)
      }
      return false
    },
    [targetExeRes],
  )

  const handleValueMappingDialog = (mapping, target) => {
    setOpenDialog(true)
    setSelectedMapping(mapping)
    setSelectedTarget(target)
  }

  const closeDialog = () => {
    setOpenDialog(false)
  }

  const getStatusIcon = useCallback(
    (mapping) => {
      if (!targetData[value]) {
        return null
      }
      const status = showAlertIcon(mapping, targetData[value]) === false ? "success" : "error"

      if (status === "success") {
        return (
          <div className={globalStyles.statusIconSuccess}>
            <span className={globalStyles.iconText}>✓</span>
          </div>
        )
      } else if (status === "error") {
        return (
          <div className={globalStyles.statusIconError}>
            <span className={globalStyles.iconText}>×</span>
          </div>
        )
      }
      return null
    },
    [targetData, value],
  )

  useEffect(() => {
    const deepCopyTargets = JSON.parse(JSON.stringify(targets))
    setTargetData(deepCopyTargets)
  }, [targets])

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value)
    setCurrentPage(1)
  }

  useEffect(() => {
    if (targetData[value]?.fieldMappings) {
      setCurrentTabData(targetData[value].fieldMappings)
    } else {
      setCurrentTabData([])
    }
  }, [value, targetData])

  const filterData = useCallback(
    (data) => {
      if (selectedFilter === "See all") {
        return data
      }

      return data.filter((item) => {
        const target = targetData[value]

        switch (selectedFilter) {
          case "Mapped successfully":
            return item.sourcefield && !item.skip && !showAlertIcon(item, target)
          case "Errors in mapping":
            return showAlertIcon(item, target)
          case "Data mismatch":
            return false
          case "Unmapped fields":
            return !item.sourcefield && !item.skip && !item.combinedFields?.length
          case "Skipped fields":
            return item.skip
          case "Required fields":
            return item.required
          default:
            return true
        }
      })
    },
    [selectedFilter, targetData, value],
  )

  const filteredTabData = useMemo(() => {
    let data = currentTabData
    if (searchTerm && data.length > 0) {
      data = data.filter(
        (item) =>
          item.targetfield.toLowerCase().includes(searchTerm.toLowerCase()) ||
          (item.sourcefield && item.sourcefield.toLowerCase().includes(searchTerm.toLowerCase())),
      )
    }
    return filterData(data)
  }, [currentTabData, searchTerm, filterData])

  const totalPages = Math.ceil(filteredTabData.length / itemsPerPage)
  const paginatedData = useMemo(() => {
    return filteredTabData.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage)
  }, [filteredTabData, currentPage, itemsPerPage])

  const handlePageChange = useCallback(
    (page) => {
      if (page >= 1 && page <= totalPages) {
        setCurrentPage(page)
      }
    },
    [totalPages],
  )

  // FIXED: handleCheckboxChange function - moved after paginatedData definition
  const handleCheckboxChange = useCallback(
    (tabIndex, displayIndex) => {
      // Add bounds checking
      if (!paginatedData || displayIndex >= paginatedData.length || displayIndex < 0) {
        console.error("Invalid display index or paginatedData not available")
        return
      }

      // Get the actual mapping from the filtered/paginated data
      const actualMapping = paginatedData[displayIndex]

      if (!actualMapping || !actualMapping.targetfield) {
        console.error("Invalid mapping data")
        return
      }

      // Find the original index in the full dataset
      const originalIndex = targetData[tabIndex].fieldMappings.findIndex(
        (mapping) => mapping.targetfield === actualMapping.targetfield,
      )

      if (originalIndex === -1) {
        console.error("Could not find original mapping index")
        return
      }

      const newData = [...targetData]
      const newSkipValue = !newData[tabIndex].fieldMappings[originalIndex].skip

      if (newData[tabIndex].fieldMappings[originalIndex].required && newSkipValue) {
        toast.error("Required fields cannot be skipped.", {
          position: "top-right",
        })
        return
      }

      newData[tabIndex].fieldMappings[originalIndex].skip = newSkipValue
      if (newSkipValue) {
        newData[tabIndex].fieldMappings[originalIndex].status = "skipped"
      } else {
        const field = newData[tabIndex].fieldMappings[originalIndex]
        const hasValidMapping =
          field.sourcefield ||
          (field.default !== undefined && field.default !== "") ||
          (field.combinedFields && field.combinedFields.length > 0)
        field.status = hasValidMapping || !field.required ? "success" : "error"
      }

      setTargetData(newData)

      const newTargets = [...targets]
      newTargets[tabIndex].fieldMappings[originalIndex].skip = newSkipValue

      if (newSkipValue) {
        newTargets[tabIndex].fieldMappings[originalIndex].status = "skipped"
      } else {
        const field = newTargets[tabIndex].fieldMappings[originalIndex]
        const hasValidMapping =
          field.sourcefield ||
          (field.default !== undefined && field.default !== "") ||
          (field.combinedFields && field.combinedFields.length > 0)
        field.status = hasValidMapping || !field.required ? "success" : "error"
      }

      setTargets(newTargets)
    },
    [targetData, targets, paginatedData],
  )

  // FIXED: handleRequiredFieldChange function - moved after paginatedData definition
  const handleRequiredFieldChange = useCallback(
    (tabIndex, displayIndex) => {
      // Add bounds checking
      if (!paginatedData || displayIndex >= paginatedData.length || displayIndex < 0) {
        console.error("Invalid display index or paginatedData not available")
        return
      }

      // Get the actual mapping from the filtered/paginated data
      const actualMapping = paginatedData[displayIndex]

      if (!actualMapping || !actualMapping.targetfield) {
        console.error("Invalid mapping data")
        return
      }

      // Find the original index in the full dataset
      const originalIndex = targetData[tabIndex].fieldMappings.findIndex(
        (mapping) => mapping.targetfield === actualMapping.targetfield,
      )

      if (originalIndex === -1) {
        console.error("Could not find original mapping index")
        return
      }

      const newData = [...targetData]
      const newReqValue = !newData[tabIndex].fieldMappings[originalIndex].required
      newData[tabIndex].fieldMappings[originalIndex].required = newReqValue

      if (newReqValue) {
        const field = newData[tabIndex].fieldMappings[originalIndex]
        const hasValidMapping =
          field.sourcefield ||
          (field.default !== undefined && field.default !== "") ||
          (field.combinedFields && field.combinedFields.length > 0)

        if (field.skip && newReqValue) {
          field.skip = false
        }
        field.status = hasValidMapping ? "success" : "error"
      }

      setTargetData(newData)

      const newTargets = [...targets]
      newTargets[tabIndex].fieldMappings[originalIndex].required = newReqValue
      if (newReqValue) {
        const field = newTargets[tabIndex].fieldMappings[originalIndex]
        if (field.skip) {
          field.skip = false
        }

        const hasValidMapping =
          field.sourcefield ||
          (field.default !== undefined && field.default !== "") ||
          (field.combinedFields && field.combinedFields.length > 0)
        field.status = hasValidMapping ? "success" : "error"
      }

      setTargets(newTargets)
    },
    [targetData, targets, paginatedData],
  )

  const handleSourceFieldChange = useCallback(
    (tabIndex, filteredIndex, newValue) => {
      // Add bounds checking
      if (!filteredTabData || filteredIndex >= filteredTabData.length || filteredIndex < 0) {
        console.error("Invalid filtered index or filteredTabData not available")
        return
      }

      const actualMapping = filteredTabData[filteredIndex]

      if (!actualMapping || !actualMapping.targetfield) {
        console.error("Invalid mapping data")
        return
      }

      const originalIndex = targetData[tabIndex].fieldMappings.findIndex(
        (mapping) => mapping.targetfield === actualMapping.targetfield,
      )

      if (originalIndex !== -1) {
        const newData = [...targetData]
        // Handle "None" selection by setting to null or empty string
        newData[tabIndex].fieldMappings[originalIndex].sourcefield = newValue === "None" ? null : newValue
        setTargetData(newData)

        const newTargets = [...targets]
        newTargets[tabIndex].fieldMappings[originalIndex].sourcefield = newValue === "None" ? null : newValue
        setTargets(newTargets)
      }
    },
    [targetData, targets, filteredTabData],
  )

  const handleShowPage = useCallback((newItemsPerPage) => {
    setItemsPerPage(newItemsPerPage)
    setCurrentPage(1)
  }, [])

  const handleFilterSelect = (filter) => {
    setSelectedFilter(filter)
    setShowFilterDropdown(false)
    setCurrentPage(1)
  }

  const goToNextStep = async () => {
    if (isSaving) {
      return
    }

    if (getErrorFieldsCount(targets[value]) > 0) {
      toast.error("Resolve the issues in order to move to the next step.", {
        position: "top-right",
      })
      return
    }

    try {
      // Set saving state to show loading indicator
      setIsSaving(true)
      setSavingMessage("Saving mapping configuration...")

      // Save the current state
      await saveMigration({ targets })

      setSavingMessage("Updating migration state...")
      const obj = {
        targets: targets,
        sourceExeRes: sourceExeRes,
        targetExeRes: targetExeRes,
        sourceMapRes: sourceMapRes,
        sourceResAtt: sourceResAtt,
        isNotes: isNotes,
      }

      setMigrationState((prevState) => ({
        ...prevState,
        dataMappingData: obj,
      }))

      const newValue = value + 1

      if (newValue < targets.length) {
        setSavingMessage("Moving to next entity...")
        toast.success("Mapping saved successfully!", {
          position: "top-right",
          autoClose: 2000,
        })
        setValue(newValue)
        setCurrentPage(1)

        if (targetData[newValue]?.fieldMappings) {
          setCurrentTabData(targetData[newValue].fieldMappings)
        }
      } else if (newValue === targets.length) {
        setSavingMessage("Finalizing mapping process...")
        setSelectedTab("5")

        const activityPayload = {
          email: email,
          activity: "Field Mapping Completed",
          // timestamp: Date.now(),
        }

        await postActivity(activityPayload)

        toast.success("Field mapping completed successfully!", {
          position: "top-right",
        })
      }
    } catch (error) {
      console.error("Error saving migration plan:", error)
      toast.error("Failed to save mapping. Please try again.", {
        position: "top-right",
      })
    } finally {
      setIsSaving(false)
      setSavingMessage("Saving...")
    }
  }

  const saveMigration = async (obj) => {
    try {
      if (!obj || !obj.targets || obj.targets.length === 0) {
        throw new Error("Invalid mapping data. Please check your configuration.")
      }

      const payload = {
        plan_name: templateName,
        migration_objects: [],
        updatedAt: Date.now(),
        email_id: email,
        additional_details: {
          sourceObjData: source,
          targetData: target,
          dataTypeData: dataTypeData,
          dataMappingData: obj,
          selectedObjectData: migrationState.selectedObjectData,
          selectedEntityData: migrationState.selectedEntityData,
          sourceExeRes: migrationState.sourceExeRes,
          targetExeRes: migrationState.targetExeRes,
          sourceMapRes: sourceMapRes,
          sourceResAtt: sourceResAtt,
        },
      }

      if (planId) {
        payload["plan_id"] = planId
        payload["action"] = "update"
        if (payload.createdAt) delete payload.createdAt
      } else {
        payload["action"] = "create"
      }

      const res = await saveMigrationPlan(payload)

      if (res?.message === "plan created successfully" && res?.response?.id) {
        setPlanId(res.response.id)
        return true
      } else if (res?.message === "plan updated successfully") {
        return true
      } else {
        console.warn("Unexpected response from saveMigrationPlan:", res)
        return true
      }
    } catch (error) {
      console.error("Error saving migration plan:", error)
      toast.error(
        error?.response?.data?.message || error?.message || "Failed to save migration plan. Please try again.",
        { position: "top-right" },
      )
      throw error
    }
  }

  const getAttributesFromResponseAttributes = useCallback((attributeNames, responseAttributes, parent = "") => {
    responseAttributes?.forEach((attr) => {
      const fullPath = parent ? `${parent}.${attr.attributeName}` : attr.attributeName
      attributeNames.push(fullPath)
      if (attr.subType && attr.subType.length > 0) {
        getAttributesFromResponseAttributes(attributeNames, attr.subType, fullPath)
      }
    })
  }, [])

  const findBestMatch = useCallback((key, fieldNames) => {
    const normalizeString = (str) => str.toLowerCase().replace(/[^a-z0-9]/g, "")
    const normalizedKey = normalizeString(key)
    let bestMatch = { target: "", rating: Number.POSITIVE_INFINITY }

    fieldNames.forEach((fieldName) => {
      const normalizedField = normalizeString(fieldName)

      if (key === fieldName || normalizedKey === normalizedField) {
        bestMatch = { target: fieldName, rating: 0 }
        return
      }

      if (normalizedField.includes(normalizedKey)) {
        const matchScore = Math.abs(normalizedField.length - normalizedKey.length)
        if (matchScore < bestMatch.rating) {
          bestMatch = { target: fieldName, rating: matchScore }
        }
      } else if (normalizedKey.includes(normalizedField)) {
        const matchScore = normalizedKey.length - normalizedField.length
        if (matchScore < bestMatch.rating) {
          bestMatch = { target: fieldName, rating: matchScore }
        }
      }

      const regex = new RegExp(normalizedKey.split("").join(".*"), "i")
      if (regex.test(normalizedField)) {
        const matchScore = normalizedField.length
        if (matchScore < bestMatch.rating) {
          bestMatch = { target: fieldName, rating: matchScore }
        }
      }
    })

    return bestMatch
  }, [])

  const findChoicesForDependentField = useCallback((choices, level) => {
    const result = []

    const traverse = (options, currentLevel) => {
      if (currentLevel === level) {
        options.forEach((option) => {
          if (option.nested_options) {
            result.push(...option.nested_options)
          }
        })
      } else if (currentLevel < level) {
        options.forEach((option) => {
          if (option.nested_options && option.nested_options.length > 0) {
            traverse(option.nested_options, currentLevel + 1)
          }
        })
      }
    }

    traverse(choices, 1)
    return result
  }, [])

  const generateObjectForNestedFields = useCallback(
    (ticketFields) => {
      return ticketFields?.flatMap((field) => {
        if (!field.nested_fields || field.nested_fields.length === 0) {
          return [field]
        }

        const parentProps = {
          id: field.id,
          label: field.label,
          description: field.description,
          field_type: field.field_type,
          created_at: field.created_at,
          updated_at: field.updated_at,
          position: field.position,
          required_for_closure: field.required_for_closure,
          workspace_id: field.workspace_id,
          name: field.name,
          default_field: field.default_field,
          required_for_agents: field.required_for_agents,
          customers_can_edit: field.customers_can_edit,
          label_for_customers: field.label_for_customers,
          required_for_customers: field.required_for_customers,
          displayed_to_customers: field.displayed_to_customers,
          belongs_to_section: field.belongs_to_section,
          choices: field.choices,
          nested_fields: field.nested_fields,
          sections: field.sections,
        }

        const result = [parentProps]

        field.nested_fields.forEach((nestedField) => {
          result.push({
            ...parentProps,
            id: nestedField.id,
            label: nestedField.label,
            name: nestedField.name,
            description: nestedField.description,
            created_at: nestedField.created_at,
            updated_at: nestedField.updated_at,
            field_type: "dependent_field",
            choices: findChoicesForDependentField(field.choices, nestedField.level - 1),
            nested_fields: [],
            sections: [],
          })
        })

        return result
      })
    },
    [findChoicesForDependentField],
  )

  const getTargetConnectionFields = useCallback(() => {
    targets.forEach((targets) => {
      targets.mappings = []
      if (["freshservice", "atomicworks"].includes(target.target.name)) {
        targets.fieldMappings.forEach((field) => {
          if (field.targetfield === "workspace_id") {
            field.default = target.formData.workspace_id
          }
        })
      }
    })

    if (!targetExeRes || !targetExeRes.ticketFields || filteredAttributes.length === 0) {
      return
    }

    const localMappedSourceFields = new Set(mappedSourceFields)

    const ticketFields = targetExeRes.ticketFields
    let target_custom_objects = []

    target_custom_objects = ticketFields
      ?.filter(
        (field) =>
          field.field_type?.startsWith("custom_") ||
          field.name?.startsWith("cf") ||
          field.field_type?.includes("nested_field"),
      )
      .flatMap((field) => {
        let obj_type = "string"

        switch (field.field_type) {
          case "custom_number":
          case "number":
            obj_type = "number"
            break
          case "custom_checkbox":
          case "checkbox":
            obj_type = "boolean"
            break
          case "custom_multi_select_dropdown":
          case "multi_select_dropdown":
            obj_type = "array"
            break
          case "custom_date":
          case "date":
            obj_type = "datetime"
            break
        }

        if (isDependencyAdded && (field.field_type === "custom_dropdown" || field.field_type === "dropdown")) {
          obj_type = "number"
        }

        const generateFieldObject = (field, parentFieldName = null, level = 1, isNested = false) => {
          const bestMatch = findBestMatch(field.name, filteredAttributes)
          let matchedSourceField = ""

          if (bestMatch?.target && !localMappedSourceFields.has(bestMatch.target)) {
            localMappedSourceFields.add(bestMatch.target)
            matchedSourceField = bestMatch.target
          }

          return {
            sourcefield: matchedSourceField,
            targetfield: field.name,
            override: [],
            attribute: field.name,
            type: obj_type,
            description: field.description,
            required: field.required_for_agents ? field.required_for_agents : false,
            default: "",
            isSourceFieldAvailable: true,
            mappingEnabled: isNested || field.choices?.length > 0,
            skip: false,
            showFieldOnInitialScreen: true,
            is_custom_field: true,
            custom_field_key: "custom_fields",
            parent_field_name: parentFieldName,
            nested_level: level,
            is_nested: isNested,
          }
        }

        const result = [generateFieldObject(field)]

        if (field.field_type?.includes("nested_field") && field.nested_fields?.length > 0) {
          result.push(
            ...field.nested_fields.map((nestedField) =>
              generateFieldObject(nestedField, field.name, nestedField.level, true),
            ),
          )
        }

        return result
      })

    const processedTicketFields = generateObjectForNestedFields(ticketFields)

    const updatedTargets = JSON.parse(JSON.stringify(targets))

    processedTicketFields?.forEach((field) => {
      if (field.required_for_agents) {
        updatedTargets.forEach((target) => {
          if (target.name === selectedEntityData?.mainTarget?.[0]?.name) {
            target.fieldMappings?.forEach((temp_field) => {
              if (temp_field.targetfield === field.name) {
                temp_field.required = true
                temp_field.showFieldOnInitialScreen = true
                temp_field.skip = false
              }
            })
          }
        })
      }
    })

    updatedTargets.forEach((target) => {
      target.automap = true

      if (target.name === selectedEntityData?.mainTarget?.[0]?.name && target_custom_objects.length > 0) {
        const newMappings = target_custom_objects.filter(
          (customObj) =>
            !target.fieldMappings?.some((existingObj) => existingObj.targetfield === customObj.targetfield),
        )

        if (target.fieldMappings && newMappings.length > 0) {
          target.fieldMappings = [...target.fieldMappings, ...newMappings]
        } else if (newMappings.length > 0) {
          target.fieldMappings = [...newMappings]
        }
      }
      autoMap(target)
    })

    setMappedSourceFields(localMappedSourceFields)

    return updatedTargets
  }, [
    targetExeRes,
    filteredAttributes,
    mappedSourceFields,
    targets,
    findBestMatch,
    isDependencyAdded,
    selectedEntityData,
    generateObjectForNestedFields,
  ])

  useEffect(() => {
    if (targets.length > 0 && filteredAttributes.length > 0 && targetExeRes?.ticketFields) {
      setShouldRefreshTargets(true)
    }
  }, [targets?.length, filteredAttributes?.length, targetExeRes?.ticketFields])

  useEffect(() => {
    if (shouldRefreshTargets) {
      const updatedTargets = getTargetConnectionFields()
      if (updatedTargets) {
        setTargets(updatedTargets)
      }
      setShouldRefreshTargets(false)
    }
  }, [shouldRefreshTargets, getTargetConnectionFields])

  const calculateStats = useCallback(() => {
    let successful = 0
    let errors = 0
    let mismatches = 0
    let unmapped = 0

    targetData.forEach((target) => {
      target.fieldMappings?.forEach((mapping) => {
        if (mapping.required && !mapping.sourcefield) {
          errors++
        } else if (mapping.status === "error") {
          mismatches++
        } else if (!mapping.sourcefield && !mapping.skip) {
          unmapped++
        } else if (mapping.sourcefield && !mapping.skip) {
          successful++
        }
      })
    })

    return { successful, errors, mismatches, unmapped }
  }, [targetData])

  const stats = calculateStats()

  const restoreDefaults = () => {
    setShouldRefreshTargets(true)
  }

  const getValuesByColumnName = (columnName) => {
    if (source.uniqueSourceValues) {
      const columnObject = source.uniqueSourceValues.find((obj) => obj.column === columnName)
      return columnObject ? columnObject.values : []
    }
    return []
  }

  const getChoiceMappingAttribute = (sourceMappingConfig) => {
    return sourceMappingConfig?.mappingAttribute?.trim() ? sourceMappingConfig.mappingAttribute : "title"
  }

  const getSourceFieldDropdownValues = (name, attribute) => {
    if (source.type === "csv") {
      if (source.uniqueSourceValues) {
        const key = attribute.sourcefield !== "" ? attribute.sourcefield : undefined
        return getValuesByColumnName(key)
      }
    } else if (source.type === "api") {
      const sourceMappingKey = getSourceMappingObjectKey(
        selectedObjectData?.sourceMappingConfig ? selectedObjectData.sourceMappingConfig : "",
      )
      const choiceKey = getChoiceMappingAttribute(
        selectedObjectData?.sourceMappingConfig ? selectedObjectData.sourceMappingConfig : "",
      )
      const fieldName = getSourceMappingFieldName(
        selectedObjectData?.sourceMappingConfig ? selectedObjectData.sourceMappingConfig : "",
      )

      if (sourceExeRes) {
        if (name === "Departments") {
          return sourceExeRes[name].map((obj) => obj.title)
        } else if (name === selectedEntityData?.mainTarget[0].name) {
          if (sourceExeRes[sourceMappingKey] && sourceExeRes[sourceMappingKey].length > 0) {
            for (const field of sourceExeRes[sourceMappingKey]) {
              if (attribute.sourcefield === field[fieldName]) {
                return field.choices.map((choice) => choice[choiceKey])
              }
            }
          }
        } else {
          if (sourceExeRes[name]) {
            return sourceExeRes[name].map((obj) => obj.name)
          }
          return []
        }
      }
      return []
    }
    return []
  }

  const getTargetFieldDropdownValues = (temp) => {
    if (targetExeRes && targetExeRes[temp]) {
      return targetExeRes[temp]
    }
    return []
  }

  const getTargetFieldDropdownValues1 = (name, field) => {
    const temp = name.toLowerCase()
    let searchFieldName = "label"
    if (field.is_custom_field) {
      searchFieldName = "name"
    }
    if (name === selectedEntityData?.mainTarget[0].name && targetExeRes && targetExeRes.ticketFields) {
      return getValuesByColumnName1(targetExeRes.ticketFields, field.targetfield, searchFieldName, "choices")
    }
    return []
  }

  const getValuesByColumnName1 = (arr, columnName, searchFieldName, returnFieldName) => {
    if (arr) {
      if (columnName !== "sub_category" && columnName !== "item_category") {
        const columnObject = arr.find((obj) => {
          if (obj[searchFieldName] && typeof obj[searchFieldName] === "string") {
            if (obj[searchFieldName].toLowerCase() === columnName.toLowerCase()) {
              return true
            }
          }
          return false
        })
        return columnObject ? columnObject[returnFieldName] : []
      } else if (columnName == "sub_category") {
        const tempColumnName = "category"
        const columnObject = arr.find((obj) => {
          if (obj[searchFieldName] && typeof obj[searchFieldName] === "string") {
            if (obj[searchFieldName].toLowerCase() === tempColumnName.toLowerCase()) {
              return true
            }
          }
          return false
        })
        const choices = columnObject ? columnObject[returnFieldName] : []
        const sub_categories = []
        if (choices && choices.length > 0) {
          for (const item of choices) {
            if (item.nested_options && item.nested_options.length > 0) {
              for (const options of item.nested_options) {
                sub_categories.push(options)
              }
            }
          }
        }
        return sub_categories
      } else if (columnName == "item_category") {
        const tempColumnName = "category"
        const columnObject = arr.find((obj) => {
          if (obj[searchFieldName] && typeof obj[searchFieldName] === "string") {
            if (obj[searchFieldName].toLowerCase() === tempColumnName.toLowerCase()) {
              return true
            }
          }
          return false
        })
        const choices = columnObject ? columnObject[returnFieldName] : []
        const item_category = []
        if (choices && choices.length > 0) {
          for (const item of choices) {
            if (item.nested_options && item.nested_options.length > 0) {
              for (const options of item.nested_options) {
                if (options.nested_options && options.nested_options.length > 0) {
                  for (const item1 of options.nested_options) {
                    item_category.push(item1)
                  }
                }
              }
            }
          }
        }
        return item_category
      }
    }
    return []
  }

  const getTargetFieldDropdownDisplayValues = (group, name) => {
    const selectedObject = selectedEntityData?.mappingTargets?.find((obj) => obj.name === name)

    if (selectedObject.mappingDisplayField) {
      return group[selectedObject.mappingDisplayField]
    }

    return ""
  }

  const autoMap = (target, emptyColumns = []) => {
    const updatedTarget = JSON.parse(JSON.stringify(target))

    if (updatedTarget.fieldMappings) {
      for (const field of updatedTarget.fieldMappings) {
        if (
          ((field.sourcefield === "" &&
            field.default === "" &&
            (!field.combinedFields || (field.combinedFields && field.combinedFields.length === 0))) ||
            emptyColumns.includes(field.sourcefield)) &&
          !field.required
        ) {
          field.skip = true
        } else {
          field.skip = false
        }

        if (updatedTarget.name === selectedEntityData?.mainTarget[0].name) {
          if (isRequired(field.targetfield)) {
            field.required = true
          }
        }

        if (field.mappingEnabled) {
          const sourceGroups = getSourceFieldDropdownValues(updatedTarget.name, field)

          let targetGroups = []
          if (updatedTarget.name !== selectedEntityData?.mainTarget[0].name) {
            targetGroups = getTargetFieldDropdownValues(updatedTarget.name)
          } else {
            targetGroups = getTargetFieldDropdownValues1(updatedTarget.name, field)
          }

          const matchedGroups = []

          sourceGroups.forEach((sourceGroup) => {
            const lowercaseSourceGroup = sourceGroup ? sourceGroup.toLowerCase() : ""
            let targetIndex = -1

            if (updatedTarget.name !== selectedEntityData?.mainTarget[0].name) {
              targetIndex = targetGroups.findIndex(
                (targetGroup) =>
                  getTargetFieldDropdownDisplayValues(targetGroup, updatedTarget.name).toLowerCase() ===
                  lowercaseSourceGroup,
              )
            } else {
              targetIndex = targetGroups.findIndex(
                (targetGroup) =>
                  targetGroup && targetGroup.value && targetGroup.value.toLowerCase() === lowercaseSourceGroup,
              )
            }

            if (targetIndex !== -1) {
              if (field.type === "array") {
                matchedGroups.push({
                  sourcevalue: [sourceGroup],
                  targetvalue: [targetGroups[targetIndex]],
                })
              } else {
                matchedGroups.push({
                  sourcevalue: [sourceGroup],
                  targetvalue: targetGroups[targetIndex],
                })
              }
            }
          })

          if (matchedGroups.length > 0) {
            field.mappings = matchedGroups
          }
        }
      }
    }

    return updatedTarget
  }

  const getMappedFieldsCount = (target) => {
    return (
      target?.fieldMappings?.filter(
        (field) =>
          field.showFieldOnInitialScreen &&
          !field.skip &&
          !showAlertIcon(field, target) &&
          ((field.sourcefield && field.sourcefield !== "") ||
            field.override?.length > 0 ||
            (field.default ?? "") !== "" ||
            field.combinedFields?.length > 0),
      ).length || 0
    )
  }

  const getErrorFieldsCount = (target) => {
    return target?.fieldMappings?.filter((field) => showAlertIcon(field, target)).length || 0
  }

  const getTotalFieldsCount = (target) =>
    target?.fieldMappings?.filter((mapping) => mapping.showFieldOnInitialScreen).length || 0

  const getSkippedFieldsCount = (fieldMappings) =>
    fieldMappings?.filter((field) => field.showFieldOnInitialScreen && field.skip).length || 0

  return (
    <div style={{ position: "relative" }}>
      {isSaving && (
        <div
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: "rgba(255, 255, 255, 0.8)",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            zIndex: 1000,
            borderRadius: "8px",
          }}
        >
          <LoaderSpinner fullpage={false} />
        </div>
      )}
      <p></p>
      <div className={styles.dFlex}>
        <div style={{ display: "flex", gap: "20px", marginLeft: "auto" }}>
          <div className={styles.dFlex}>
            <img
              src="/assets/help.png"
              alt="Help"
              className={globalStyles.helpIcon}
              onClick={() => {
                displayArticle("Why do all these tabs appear?")
              }}
            />
            <span className={globalStyles.guideName}>Why do all these tabs appear?</span>
          </div>
        </div>
      </div>

      <Tabs
        value={value}
        onChange={handleChange}
        variant="scrollable"
        scrollButtons="auto"
        allowScrollButtonsMobile
        aria-label="data mapping tabs"
        className={styles.tab}
        sx={{
          width: "100%",
          "& .MuiTabs-flexContainer": {
            width: "100%",
          },
          "& .MuiTabs-scroller": {
            overflowX: "auto",
          },
          "& .MuiTabs-indicator": {
            backgroundColor: "#FFFFFF",
          },
          "& .MuiTabs-scrollButtons": {
            color: "#FFFFFF",
          },
          "& .Mui-disabled": {
            color: "rgba(255, 255, 255, 0.3)",
          },
          minHeight: "35px",
        }}
      >
        {targetData.map((item, index) => (
          <Tab
            key={index}
            label={item.name}
            style={{ pointerEvents: index > value ? "none " : "", fontSize: "11px", fontWeight: "bold" }}
            sx={{
              minHeight: "35px",
              padding: "6px 10px",
              color: "#DCDAD9",
              borderRight: "1px solid #DCDAD9",
              borderBottom: "1px solid #DCDAD9",
              borderTop: "1px solid #DCDAD9",
              borderLeft: index === 0 ? "1px solid #DCDAD9" : "none",
              borderRadius: 0,
              minWidth: "150px",
              padding: "10px 15px",
              flex: 1,
              "&.Mui-selected": {
                color: "#EA5822",
                fontWeight: "700",
                fontsize: "12px",
                backgroundColor: "#FFFFFF",
                boxShadow: "3px -5px 7px 0px #00000026 !important",
              },
            }}
          />
        ))}
      </Tabs>
      <div style={{ padding: "20px 0px 20px 20px" }}>
        <div className={styles.dFlex}>
          <div className={globalStyles.selectionName}>MAP YOUR DATA FIELDS FOR MIGRATION</div>
          <div style={{ display: "flex", gap: "20px", marginLeft: "auto" }}>
            <div className={styles.dFlex}>
              <img
                src="/assets/help.png"
                alt="Help"
                className={globalStyles.helpIcon}
                onClick={() => {
                  displayArticle("How do I fix errors and mismatched data?")
                }}
              />
              <span className={globalStyles.guideName}>How do I fix errors and mismatched data?</span>
            </div>
          </div>
        </div>
 {/* <div className={styles.dFlex} style={{ justifyContent: "stretch", gap: "10px" }}>
          <div className={styles.container}>
            <CheckIcon className={styles.iconStyle} />
            <div className={globalStyles.poppinsHeaderStyle} style={{ fontSize: "30px" }}>
              {getMappedFieldsCount(targets[value])}
            </div>
            <div className={styles.boxName}>
              Successfully <br /> mapped
            </div>
          </div>
          <div className={styles.container}>
            <span style={{ fontWeight: "bold", fontSize: "20px", color: "#746b68" }}>!</span>
            <div className={globalStyles.poppinsHeaderStyle} style={{ fontSize: "30px" }}>
              {getErrorFieldsCount(targets[value])}
            </div>
            <div className={styles.boxName}>
              Errors in <br /> mapping
            </div>
          </div>
          <div className={styles.container}>
            <HiNoSymbol className={styles.iconStyle} />
            <div className={globalStyles.poppinsHeaderStyle} style={{ fontSize: "30px" }}>
              {getTotalFieldsCount(targets[value]) -
                getMappedFieldsCount(targets[value]) -
                getSkippedFieldsCount(targets[value]?.fieldMappings)}
            </div>
            <div className={styles.boxName}>
              Unmapped <br /> fields
            </div>
          </div>
        </div> */}
        <div className={styles.dFlex} style={{ justifyContent: "stretch", gap: "8px" }}>
          <div className={styles.container} style={{ padding: "5px", height: "40px" }}>
            <CheckIcon className={styles.iconStyle} style={{ marginLeft: "10px", width: "20px", height: "20px" }} />
            <div className={globalStyles.poppinsHeaderStyle} style={{ fontSize: "24px" }}>
              {getMappedFieldsCount(targets[value])}
            </div>
            <div className={styles.boxName} style={{ fontSize: "12px" }}>
              Successfully mapped
            </div>
          </div>
          <div className={styles.container} style={{ padding: "5px", height: "40px" }}>
            <span style={{ marginLeft: "10px", fontWeight: "bold", fontSize: "24px", color: "#746b68" }}>!</span>
            <div className={globalStyles.poppinsHeaderStyle} style={{ fontSize: "24px" }}>
              {getErrorFieldsCount(targets[value])}
            </div>
            <div className={styles.boxName} style={{ fontSize: "12px" }}>
              Errors in mapping
            </div>
          </div>
          <div className={styles.container} style={{ padding: "5px", height: "40px" }}>
            <HiNoSymbol className={styles.iconStyle} style={{ marginLeft: "10px", width: "20px", height: "20px" }} />
            <div className={globalStyles.poppinsHeaderStyle} style={{ fontSize: "24px" }}>
              {getTotalFieldsCount(targets[value]) -
                getMappedFieldsCount(targets[value]) -
                getSkippedFieldsCount(targets[value]?.fieldMappings)}
            </div>
            <div className={styles.boxName} style={{ fontSize: "12px" }}>
              Unmapped fields
            </div>
          </div>
        </div>

        <div className={styles.dFlex} style={{ marginTop: "10px" }}>
{/* <button className={`${styles.dFlex} ${styles.buttonStyle}`} style={{ gap: "10px" }} onClick={restoreDefaults}>
            <HiArrowPath className={styles.iconStyle} />
            Restore defaults
          </button> */}
          <div className={styles.dFlex} style={{ marginLeft: "auto", justifyContent: "center", alignItems: "center" }}>
            <div className={globalStyles.searchWrapper}>
              <SearchIcon className={globalStyles.searchIcon} />
              <input
                type="text"
                placeholder="Search..."
                className={globalStyles.searchInput}
                value={searchTerm}
                onChange={handleSearchChange}
                onFocus={(e) => {
                  e.target.style.width = "200px"
                  e.target.placeholder = "Typing..."
                }}
                onBlur={(e) => {
                  e.target.style.width = "80px"
                  e.target.placeholder = "Search..."
                }}
              />
            </div>
            <div className={styles.filterContainer} ref={filterDropdownRef}>
              <button
                className={`${styles.dFlex} ${styles.buttonStyle}`}
                style={{ gap: "10px", marginBottom: "10px" }}
                onClick={() => setShowFilterDropdown(!showFilterDropdown)}
              >
                <HiOutlineFunnel className={styles.iconStyle} />
                {selectedFilter}
              </button>
              {showFilterDropdown && (
                <div className={styles.filterDropdown}>
                  {getFilterOptions().map((option, index) => (
                    <div key={index} className={styles.filterOption} onClick={() => handleFilterSelect(option)}>
                      {option}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {targetData[value]?.name === "Conversations" && (
          <div style={{ marginTop: "20px", display: "flex", alignItems: "center", gap: "10px" }}>
            <input
              type="checkbox"
              checked={isNotes}
              onChange={(e) => setIsNotes(e.target.checked)}
              aria-label="Create conversations as notes"
              style={{
                width: "16px",
                height: "16px",
                cursor: "pointer",
                accentColor: "black",
              }}
            />
            <label className={globalStyles.interStyle} style={{ paddingBottom: "10px", cursor: "pointer" }}>
              Create conversations as notes
            </label>
          </div>
        )}

        <div className={styles.tableContainer}>
          <table className={globalStyles.table}>
            <thead className={globalStyles.tableHeader}>
              <tr className={globalStyles.rowStyles}>
                <th className={globalStyles.headerCell}>Status</th>
                <th className={globalStyles.headerCell}>Source field</th>
                <th className={globalStyles.headerCell}>Target field</th>
                <th className={globalStyles.headerCell}>Required field</th>
                <th className={`${globalStyles.headerCell} ${globalStyles.centerText}`}>Skip field</th>
                <th className={`${globalStyles.headerCell} ${globalStyles.centerText}`}>Map field</th>
              </tr>
            </thead>
            <tbody>
              {paginatedData.length > 0 ? (
                paginatedData.map(
                  (mapping, index) =>
                    mapping.showFieldOnInitialScreen && (
                      <tr key={index} className={globalStyles.tableRow}>
                        <td className={globalStyles.cell}>
                          <div className={globalStyles.centerContent}>{getStatusIcon(mapping)}</div>
                        </td>
                        <td className={globalStyles.cell} style={{ zIndex: "9999", fontSize: "12px" }}>
                          <div className={styles.fieldWithIcon}>
                            <img
                              src={getDataTypeIcon(mapping.type) || "/placeholder.svg"}
                              alt={mapping.type}
                              className={styles.dataTypeIcon}
                            />
                            <Dropdown
                              value={mapping.sourcefield}
                              options={["None", ...filteredAttributes]}
                              onChange={(newValue) =>
                                handleSourceFieldChange(value, index, newValue === "None" ? null : newValue)
                              }
                              placeholder="Select Source"
                              required={mapping.required}
                              none={true}
                            />
                          </div>
                        </td>
                        <td className={globalStyles.cell}>
                          <div className={styles.fieldWithIcon}>
                            <img
                              src={getDataTypeIcon(mapping.type) || "/placeholder.svg"}
                              alt={mapping.type}
                              className={styles.dataTypeIcon}
                            />
                            <span className={styles.fieldName}>{mapping.targetfield || "Select Target"} </span>
                            <span className={styles.fieldName}> {mapping.required ? "*" : ""} </span>
                          </div>
                        </td>
                        <td className={globalStyles.cell}>
                          <FormControlLabel
                            style={{ marginLeft: "auto" }}
                            control={
                              <Switch
                                checked={mapping.required}
                                onChange={(event) => handleRequiredFieldChange(value, index, event.target.checked)}
                                sx={{
                                  width: 36,
                                  height: 20,
                                  padding: 0,
                                  "& .MuiSwitch-switchBase": {
                                    padding: 0,
                                    margin: "2px",
                                    transitionDuration: "300ms",
                                    "&.Mui-checked": {
                                      transform: "translateX(16px)",
                                      color: "#fff",
                                      "& + .MuiSwitch-track": {
                                        backgroundColor: "#E97451",
                                        opacity: 1,
                                        border: 0,
                                      },
                                      "& .MuiSwitch-thumb": {
                                        backgroundColor: "#fff",
                                        width: 14,
                                        height: 14,
                                        backgroundImage: "url('/assets/locked.png')",
                                        backgroundSize: "100% 100%",
                                        backgroundRepeat: "no-repeat",
                                        backgroundPosition: "center",
                                      },
                                      "&.Mui-disabled + .MuiSwitch-track": {
                                        opacity: 0.5,
                                      },
                                    },
                                  },
                                  "& .MuiSwitch-thumb": {
                                    backgroundColor: "#fff",
                                    boxSizing: "border-box",
                                    width: 14,
                                    height: 14,
                                    borderRadius: "50%",
                                    transition: "width 0.2s, height 0.2s",
                                    backgroundImage: mapping.required ? "url('/assets/locked.png')" : "none",
                                    backgroundSize: "100% 100%",
                                    backgroundRepeat: "no-repeat",
                                    backgroundPosition: "center",
                                  },
                                  "& .MuiSwitch-track": {
                                    borderRadius: 20,
                                    backgroundColor: "#B9B5B3",
                                    opacity: 1,
                                    transition: "background-color 0.5s",
                                  },
                                }}
                              />
                            }
                          />
                        </td>
                        <td className={globalStyles.cell}>
                          <div className={globalStyles.centerContent}>
                            {mapping.required === false && (
                              <div>
                                <FormControlLabel
                                  style={{ marginLeft: "auto" }}
                                  control={
                                    <Switch
                                      checked={mapping.skip}
                                      onChange={() => handleCheckboxChange(value, index)}
                                      sx={{
                                        width: 36,
                                        height: 20,
                                        padding: 0,
                                        "& .MuiSwitch-switchBase": {
                                          padding: 0,
                                          margin: "2px",
                                          transitionDuration: "300ms",
                                          "&.Mui-checked": {
                                            transform: "translateX(16px)",
                                            color: "#fff",
                                            "& + .MuiSwitch-track": {
                                              backgroundColor: "#E97451",
                                              opacity: 1,
                                              border: 0,
                                            },
                                            "& .MuiSwitch-thumb": {
                                              backgroundColor: "#fff",
                                              width: 14,
                                              height: 14,
                                              backgroundImage: "url('/assets/skip.png')",
                                              backgroundSize: "100% 100%",
                                              backgroundRepeat: "no-repeat",
                                              backgroundPosition: "center",
                                            },
                                            "&.Mui-disabled + .MuiSwitch-track": {
                                              opacity: 0.5,
                                            },
                                          },
                                        },
                                        "& .MuiSwitch-thumb": {
                                          backgroundColor: "#fff",
                                          boxSizing: "border-box",
                                          width: 14,
                                          height: 14,
                                          borderRadius: "50%",
                                          transition: "width 0.2s, height 0.2s",
                                          backgroundImage: mapping.skip ? "url('/assets/skip.png')" : "none",
                                          backgroundSize: "100% 100%",
                                          backgroundRepeat: "no-repeat",
                                          backgroundPosition: "center",
                                        },
                                        "& .MuiSwitch-track": {
                                          borderRadius: 20,
                                          backgroundColor: "#B9B5B3",
                                          opacity: 1,
                                          transition: "background-color 0.5s",
                                        },
                                      }}
                                    />
                                  }
                                />
                              </div>
                            )}
                          </div>
                        </td>
                        <td className={globalStyles.cell}>
                          <div className={globalStyles.centerContent}>
                            {mapping.mappingEnabled ? (
                              <CogIcon
                                className={globalStyles.closeIcon}
                                onClick={() => handleValueMappingDialog(mapping, targetData[value])}
                              />
                            ) : (
                              <div className={styles.settingsContainer}>
                                <CogIcon
                                  className={globalStyles.closeIcon}
                                  onClick={() => {
                                    setActiveSettingIndex(index)
                                    setShowSettingsDropdown((prev) => ({
                                      ...prev,
                                      [index]: !prev[index],
                                    }))
                                  }}
                                  ref={(el) => {
                                    settingsDropdownRef.current[index] = el
                                  }}
                                />
                                {showSettingsDropdown[index] && activeSettingIndex === index && (
                                  <div
                                    className={styles.settingsDropdown}
                                    ref={(el) => {
                                      settingsDropdownRef.current.dropdown = el
                                    }}
                                  >
                                    {!mapping.mappingEnabled && (
                                      <div
                                        className={styles.settingsOption}
                                        onClick={() => handleDefaultMappingDialog(mapping, targetData[value])}
                                      >
                                        Default values
                                      </div>
                                    )}
                                    {!mapping.mappingEnabled && mapping.sourcefield && mapping.sourcefield != "" && (
                                      <div
                                        className={styles.settingsOption}
                                        onClick={() => handleOverrideValuesDialog(mapping, targetData[value])}
                                      >
                                        Override values
                                      </div>
                                    )}
                                    {!mapping.mappingEnabled &&
                                      (!mapping.sourcefield || mapping.sourcefield === "") && (
                                        <div
                                          className={styles.settingsOption}
                                          onClick={() => handleCombineFieldsDialog(mapping, targetData[value])}
                                        >
                                          Combine Fields
                                        </div>
                                      )}
                                    {!mapping.mappingEnabled && mapping?.isLookupEnabled && (
                                      <div
                                        className={styles.settingsOption}
                                        onClick={() => handleAdvancedMappingDialog(mapping, targetData[value])}
                                      >
                                        Advanced
                                      </div>
                                    )}
                                  </div>
                                )}
                              </div>
                            )}
                          </div>
                        </td>
                      </tr>
                    ),
                )
              ) : (
                <tr>
                  <td colSpan={6} className={styles.noDataCell}>
                    <div className={styles.noDataFound}>No data found for the selected filter</div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
          <Dialog open={openDialog} maxWidth="md" fullWidth>
            <DialogContent style={{ padding: "0", overflow: "auto" }}>
              <ValueMapping
                close={() => setOpenDialog(false)}
                attribute={selectedMapping}
                target={selectedTarget}
                selectedEntityData={selectedEntityData}
                sourceMapRes={sourceMapRes}
                source={source}
                targetData={target}
                selectedObjectData={selectedObjectData}
                targetExeRes={targetExeRes}
                onSave={handleValueMappingSave}
              />
            </DialogContent>
          </Dialog>
          <Dialog open={openCombineFieldsDialog} maxWidth="md" fullWidth>
            <DialogContent style={{ padding: "0", overflow: "auto" }}>
              <CombineFields
                close={() => setOpenCombineFieldsDialog(false)}
                attribute={selectedMapping}
                target={selectedTarget}
                onSave={handleSaveCombinedFields}
                sourceFields={filteredAttributes}
              />
            </DialogContent>
          </Dialog>

          <Dialog open={openOverrideDialog} maxWidth="md" fullWidth>
            <DialogContent style={{ padding: "0", overflow: "auto" }}>
              <OverrideValues
                close={() => setOpenOverrideDialog(false)}
                attribute={selectedMapping}
                target={selectedTarget}
                onSave={handleSaveOverrideValues}
              />
            </DialogContent>
          </Dialog>

          <Dialog open={openAdvancedDialog} maxWidth="md" fullWidth>
            <DialogContent style={{ padding: "0", overflow: "auto" }}>
              <Advanced
                close={() => setOpenAdvancedDialog(false)}
                attribute={selectedMapping}
                target={selectedTarget}
                onSave={handleSaveAdvancedCode}
              />
            </DialogContent>
          </Dialog>

          <Dialog open={openDefaultDialog} maxWidth="md" fullWidth>
            <DialogContent style={{ padding: "0", overflow: "auto" }}>
              <DefaultValue
                close={() => setOpenDefaultDialog(false)}
                attribute={selectedMapping}
                target={selectedTarget}
                onSave={handleSaveDefaultValue}
              />
            </DialogContent>
          </Dialog>

          <div style={{ display: "flex", alignItems: "center", gap: "10px", marginTop: "30px" }}>
            <span className={globalStyles.interStyle} style={{ color: "#170903", width: "80px" }}>
              Page no
            </span>
            {[...Array(totalPages)].map((_, index) => (
              <button
                key={index + 1}
                className={` ${currentPage === index + 1 ? globalStyles.activePage : globalStyles.pageButton}`}
                onClick={() => handlePageChange(index + 1)}
              >
                {String(index + 1).padStart(2, "0")}
              </button>
            ))}
            <span className={globalStyles.interStyle} style={{ color: "#170903", marginLeft: "30px" }}>
              Show
            </span>
            {showPages.map((pageLimit, index) => (
              <button
                key={index}
                className={`${itemsPerPage === pageLimit ? globalStyles.activePage : globalStyles.pageButton}`}
                onClick={() => handleShowPage(pageLimit)}
              >
                {String(pageLimit).padStart(2, "0")}
              </button>
            ))}

            <div style={{ display: "flex", justifyContent: "flex-end", width: "100%", marginBottom: "30px" }}>
              <button
                className={globalStyles.mainButton}
                style={{
                  marginLeft: "auto",
                  margin: "0",
                  opacity: isSaving ? 0.7 : 1,
                  cursor: isSaving ? "not-allowed" : "pointer",
                }}
                onClick={goToNextStep}
                disabled={isSaving}
              >
                {isSaving ? savingMessage : "Confirm mapping & Review"}
              </button>
            </div>
          </div>
        </div>
      </div>
      <ToastContainer
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
        style={{
          fontFamily: "Inter",
        }}
        toastStyle={{
          fontFamily: "Inter",
          fontWeight: "bold",
        }}
      />
    </div>
  )
}
