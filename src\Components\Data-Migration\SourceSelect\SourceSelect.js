import { useEffect, useState, useContext } from "react"
import styles from "./SourceSelect.module.css"
import SourceTargetSelector from "../Source-target-selector/Source-target-selector"
import { CheckIcon, MinusIcon } from "@heroicons/react/solid"
import globalStyles from "../../globalStyles.module.css"
import { Dialog, DialogContent, FormControlLabel, Switch } from "@mui/material"
import { filterOptions, getMigrationProviders, postActivity, updateConnDetails, validateSource } from "../../apiService"
import UploadCSV from "../UploadCSV/uploadcsv"
import { saveMigrationPlan } from "../../apiService"
import { useMongodb } from "../../mongodbService"
import { MigrationContext } from "../Data-Migration"
import { ToastContainer, toast } from "react-toastify"
import { EyeIcon, EyeOffIcon } from "@heroicons/react/solid"
import { displayArticle } from "../../../Helper/helper"
import LoaderSpinner from "../../loaderspinner"

const SourceSelect = ({
  setSelectedTab,
  templateName,
  setPlanId,
  planId,
  setMigrationSource,
  migrationSource,
  migrationTarget,
}) => {
  const { watchMigrations } = useMongodb()
  const { migrationState, setMigrationState } = useContext(MigrationContext)

  const sourceObjData = migrationState.sourceObjData || {}
  const [email, setEmail] = useState(localStorage.getItem("email"))
  const [showModal, setShowModal] = useState(false)
  const [showUploadDialog, setShowUploadDialog] = useState(false)
  const [selectedSource, setSelectedSource] = useState(null)
  const [formData, setFormData] = useState({})
  const [queryFormData, setQueryFormData] = useState({})
  const [queryParamData, setQueryParamData] = useState([])
  const [connectionStatus, setConnectionStatus] = useState(null)
  const [checked, setChecked] = useState(false)
  const [openDialog, setOpenDialog] = useState(false)
  const [sourceData, setSourceData] = useState([])
  const [selectedFile, setSelectedFile] = useState(null)
  const [csvHeaders, setCsvHeaders] = useState([])
  const [csvUrl, setCsvUrl] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [isConfirming, setIsConfirming] = useState(false)
  const [sourceType, setSourceType] = useState("")
  const [credentialsModified, setCredentialsModified] = useState(false)

  const [customQueryParams, setCustomQueryParams] = useState([])
  const [showKeyDialog, setShowKeyDialog] = useState(false)
  const [newKeyName, setNewKeyName] = useState("")
  const [deletedCustomKeys, setDeletedCustomKeys] = useState([])
  const [visibleFields, setVisibleFields] = useState({})

  const toggleVisibility = (fieldName) => {
    setVisibleFields((prev) => ({
      ...prev,
      [fieldName]: !prev[fieldName],
    }))
  }

  const moveToNextStep = async (event) => {
    setIsConfirming(true)
    event.preventDefault()
    if (sourceType !== "csv") {
      if (credentialsModified || connectionStatus !== "success") {
        toast.error("Please validate your credentials before proceeding.", {
          position: "top-right",
          style: {
            "--toastify-icon-color-error": "black",
            "--toastify-color-progress-error": "black",
          },
        })
        setIsConfirming(false)
        return
      }
    }

    const mergedQueryParams = { ...queryFormData }

    customQueryParams.forEach((param) => {
      if (!deletedCustomKeys.includes(param.key)) {
        mergedQueryParams[param.key] = param.value
      }
    })

    deletedCustomKeys.forEach((key) => {
      delete mergedQueryParams[key]
    })

    const sourceObj = {
      source: selectedSource,
      connDetails: formData,
      queryParam: mergedQueryParams,
      connectionStatus: connectionStatus,
      selectedFile: selectedFile,
      csvHeaders: csvHeaders,
      csvUrl: selectedFile?.azureBlobUrl || csvUrl,
      checked: checked,
      type: sourceType,
      uniqueSourceValues: [],
      apiRateLimit: "",
      lengthOfCSV: "",
      deletedCustomKeys: deletedCustomKeys,
    }

    setMigrationState((prevState) => ({
      ...prevState,
      sourceObjData: sourceObj,
    }))
    const newPlanId = await saveMigration(sourceObj)
    if (newPlanId) {
      const url = new URL(window.location.href)
      url.searchParams.delete("planId")
      url.searchParams.set("plan_id", newPlanId)
      window.history.replaceState({}, "", url.toString())
    }    // Show success toast notification
    toast.success(`Connected to ${selectedSource.name} profile!`, {
      position: "top-right",
      autoClose: 3000,
    })

    setOpenDialog(true)

    setTimeout(() => {
      setSelectedTab("2")
      setIsConfirming(false)
    }, 2000)
  }

  const saveMigration = async (sourceObj) => {
    try {
      const payload = {
        plan_name: templateName,
        migration_source: migrationSource,
        migration_target: migrationTarget,
        migration_objects: [],
        updatedAt: Date.now(),
        email_id: email,
        additional_details: {
          sourceObjData: sourceObj,
          targetData: migrationState.targetData,
          dataTypeData: migrationState.dataTypeData,
          dataMappingData: migrationState.dataMappingData,
          selectedObjectData: migrationState.selectedObjectData,
          selectedEntityData: migrationState.selectedEntityData,
          sourceExeRes: migrationState.sourceExeRes,
          targetExeRes: migrationState.targetExeRes,
          sourceMapRes: migrationState.sourceMapRes || [],
          sourceResAtt: migrationState.sourceResAtt || [],
        },
      }
      if (planId) {
        payload["plan_id"] = planId
        payload["action"] = "update"
        if (payload.createdAt) delete payload.createdAt
      } else {
        payload["action"] = "create"
        payload["createdAt"] = Date.now()
        const activityPayload = {
          email: email,
          activity: "Template Created",
          // timestamp: Date.now(),
        }
        postActivity(activityPayload)
      }
      const res = await saveMigrationPlan(payload)

      if (res?.message === "plan created successfully" && res?.response?.id) {
        const newPlanId = res.response.id
        setPlanId(newPlanId)
        return newPlanId
      }
      return planId
    } catch (error) {
      toast.error(error?.response?.data?.message || "Something went wrong!", {
        position: "top-right",
        style: {
          "--toastify-icon-color-error": "black",
          "--toastify-color-progress-error": "black",
        },
      })
      return null
    }
  }

  const handleCsvUploaded = (fileData) => {
    setSelectedFile(fileData)
    setCsvUrl(fileData.azureBlobUrl || "")
    setCsvHeaders(fileData.headers || [])
    setShowUploadDialog(false)
  }
  const [initialDataLoaded, setInitialDataLoaded] = useState(false)

  useEffect(() => {
    // Check if this is a new template creation (empty sourceObjData)
    if (!sourceObjData || Object.keys(sourceObjData).length === 0) {
      // Reset all state for new template
      setSelectedSource(null)
      setFormData({})
      setInitialDataLoaded(false)
      setConnectionStatus(null)
      setCredentialsModified(true)
      setChecked(false)
      setSelectedFile(null)
      setCsvHeaders([])
      setCsvUrl("")
      setSourceType("")
      setDeletedCustomKeys([])
      setQueryFormData({})
      setCustomQueryParams([])
    } else if (sourceObjData && sourceObjData.source && !initialDataLoaded) {
      setSelectedSource(sourceObjData.source)
      setFormData(sourceObjData.connDetails || {})
      setInitialDataLoaded(true)
      setConnectionStatus(sourceObjData.connectionStatus)
      setCredentialsModified(false)
      setChecked(sourceObjData.checked || false)
      setSelectedFile(sourceObjData.selectedFile)
      setCsvHeaders(sourceObjData.csvHeaders || [])
      setCsvUrl(sourceObjData.csvUrl || "")
      setSourceType(sourceObjData.type || "")
      setDeletedCustomKeys(sourceObjData.deletedCustomKeys || [])

      if (sourceObjData.source) {
        fetchFilterOptions(sourceObjData.source.name)
      }
    }

    const fetchProviders = async () => {
      try {
        const data = await getMigrationProviders()
        setSourceData(data)
      } catch (error) {
        console.error("Failed to load migration providers:", error)
      }
    }

    fetchProviders()
  }, [sourceObjData, initialDataLoaded])

  useEffect(() => {
    if (sourceObjData && sourceObjData.queryParam && queryParamData.length > 0) {
      const standardParamKeys = queryParamData.map((param) => param.key)
      const savedQueryParams = { ...sourceObjData.queryParam }

      const standardParams = {}
      Object.entries(savedQueryParams).forEach(([key, value]) => {
        if (standardParamKeys.includes(key)) {
          standardParams[key] = value
        }
      })
      setQueryFormData(standardParams)

      const customParams = []
      Object.entries(savedQueryParams).forEach(([key, value]) => {
        if (!standardParamKeys.includes(key) && !sourceObjData.deletedCustomKeys?.includes(key)) {
          customParams.push({ key, value })
        }
      })

      setCustomQueryParams(customParams)
    }
  }, [sourceObjData, queryParamData])

  useEffect(() => {
    if (selectedSource && selectedSource.name) {
      fetchFilterOptions(selectedSource.name)
    }
  }, [selectedSource])

  const fetchFilterOptions = async (name) => {
    try {
      const data = await filterOptions(name)
      if (data.response && data.response[0] && data.response[0].queryParams) {
        setQueryParamData(data.response[0].queryParams)
      }
    } catch (error) {
      console.error("Failed to load filter options:", error)
      setQueryParamData([])
    }
  }

  const handleSourceSelect = (source) => {
    console.log("handleSourceSelect called with:", source, "at:", new Date().toISOString())

    const newSource = sourceData.find((item) => item.name === source)
    setMigrationSource([newSource.id])
    localStorage.setItem("sourcePlatform", newSource.displayName || newSource.name)

    if (source === "csv") {
      setSourceType("csv")
    } else {
      setSourceType("api")
    }
    console.log("Resetting all state values for source selection")
    setSelectedSource(newSource)
    setFormData({})
    setQueryFormData({})
    setConnectionStatus(null)
    setCredentialsModified(true)
    setChecked(false)
    setSelectedFile(null)
    setCsvHeaders([])
    setCsvUrl("")
    setCustomQueryParams([])
    setDeletedCustomKeys([])

    setMigrationState((prevState) => ({
      ...prevState,
      sourceObjData: {
        source: newSource,
        connDetails: {},
        connectionStatus: null,
        type: source === "csv" ? "csv" : "api",
      },
    }))

    fetchFilterOptions(newSource.name)
  }

  const handleConnect = (e) => {
    e.preventDefault()
  }

  const handleInputChange = (e, fieldName) => {
    const newValue = e.target.value
    const oldValue = formData[fieldName] || ""
    if (["username", "password", "apikey", "instance_url"].includes(fieldName) && newValue !== oldValue) {
      setCredentialsModified(true)
      setConnectionStatus(null)
      setMigrationState((prevState) => ({
        ...prevState,
        sourceObjData: {
          ...prevState.sourceObjData,
          connectionStatus: null,
        },
      }))
    }
    setFormData((prev) => ({
      ...prev,
      [fieldName]: newValue,
    }))
  }

  const handleQueryInputChange = (e, fieldName) => {
    setQueryFormData((prev) => ({
      ...prev,
      [fieldName]: e.target.value,
    }))
  }

  const handleCustomQueryInputChange = (e, index) => {
    const updatedParams = [...customQueryParams]
    updatedParams[index].value = e.target.value
    setCustomQueryParams(updatedParams)
  }

  const removeSelectedFile = () => {
    setSelectedFile(null)
    setCsvHeaders([])
    setCsvUrl("")
  }

  const handleReset = () => {
    setFormData({})
    setQueryFormData({})
    setConnectionStatus(null)
    setChecked(false)
    setCustomQueryParams([])
    setDeletedCustomKeys([])
    setCredentialsModified(true)
    toast.info("All form fields have been reset", {
      position: "top-right",
      style: {
        "--toastify-icon-color-info": "black",
        "--toastify-color-progress-info": "black",
      },
    })
  }

  const addCustomQueryParam = () => {
    setShowKeyDialog(true)
  }

  const handleKeyDialogSubmit = () => {
    if (newKeyName.trim()) {
      const isStandardParam = queryParamData.some((param) => param.key === newKeyName.trim())

      if (isStandardParam) {
        setDeletedCustomKeys((prev) => prev.filter((key) => key !== newKeyName.trim()))
        toast.info(`Parameter "${newKeyName.trim()}" restored`, {
          position: "top-right",
          autoClose: 2000,
          style: {
            "--toastify-icon-color-info": "black",
            "--toastify-color-progress-info": "black",
          },
        })
      } else {
        const keyExists = customQueryParams.some((param) => param.key === newKeyName.trim())
        if (!keyExists) {
          setCustomQueryParams([...customQueryParams, { key: newKeyName.trim(), value: "" }])
          if (deletedCustomKeys.includes(newKeyName.trim())) {
            setDeletedCustomKeys((prev) => prev.filter((key) => key !== newKeyName.trim()))
          }
        } else {
          toast.info(`Parameter "${newKeyName.trim()}" already exists`, {
            position: "top-right",
            autoClose: 2000,
            style: {
              "--toastify-icon-color-info": "black",
              "--toastify-color-progress-info": "black",
            },
          })
        }
      }
      setNewKeyName("")
      setShowKeyDialog(false)
    }
  }

  const removeCustomQueryParam = (index) => {
    const paramToRemove = customQueryParams[index]
    setDeletedCustomKeys((prev) => [...prev, paramToRemove.key])
    if (queryFormData[paramToRemove.key]) {
      const updatedQueryFormData = { ...queryFormData }
      delete updatedQueryFormData[paramToRemove.key]
      setQueryFormData(updatedQueryFormData)
    }
    const updatedParams = customQueryParams.filter((_, i) => i !== index)
    setCustomQueryParams(updatedParams)
  }

  const removeQueryParam = (paramKey) => {
    setDeletedCustomKeys((prev) => [...prev, paramKey])
    const updatedQueryFormData = { ...queryFormData }
    delete updatedQueryFormData[paramKey]
    setQueryFormData(updatedQueryFormData)

    toast.info(`Parameter "${paramKey}" removed`, {
      position: "top-right",
      autoClose: 2000,
      style: {
        "--toastify-icon-color-info": "black",
        "--toastify-color-progress-info": "black",
      },
    })
  }

  const extractSourceDomain = (url) => {
    try {
      const trimmedUrl = url.trim()
      const parsedUrl = new URL(trimmedUrl)
      return parsedUrl.hostname
    } catch (error) {
      console.error("Invalid URL:", error)
      return null
    }
  }

  const validateSourceFun = async () => {
    if (!formData) return
    const currentFormData = { ...formData }

    setIsLoading(true)
    setConnectionStatus(null)
    const tempKey = String(currentFormData.apikey || "")

    const payload = {
      sourceName: selectedSource.name,
      domain: extractSourceDomain(currentFormData.instance_url),
      domainUrl: extractSourceDomain(currentFormData.instance_url),
      apikey: tempKey,
      queryParams: [],
    }

    if (currentFormData.username && currentFormData.password) {
      payload["username"] = currentFormData.username
      payload["password"] = currentFormData.password
    }

    try {
      const res = await validateSource(payload)
      const connPayload = {
        domain: extractSourceDomain(currentFormData.instance_url),
        accountEmail: currentFormData.username,
        userEmail: email,
        providerName: selectedSource.name,
        isSource: true,
        isTarget: false,
      }
      await updateConnDetails(connPayload)
      setMigrationState((prevState) => ({
        ...prevState,
        sourceObjData: {
          ...prevState.sourceObjData,
          connDetails: currentFormData,
          connectionStatus: "success",
        },
      }))

      setConnectionStatus("success")
      setCredentialsModified(false)
    } catch (error) {
      toast.error("Connection Failed! Please check your credentials and try again.", {
        position: "top-right",
        style: {
          "--toastify-icon-color-error": "black",
          "--toastify-color-progress-error": "black",
        },
      })
      setConnectionStatus("failed")
      setMigrationState((prevState) => ({
        ...prevState,
        sourceObjData: {
          ...prevState.sourceObjData,
          connDetails: currentFormData,
          connectionStatus: "failed",
        },
      }))
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <>
      {isConfirming && <div
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: "rgba(255, 255, 255, 0.8)",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          zIndex: 1000,
          borderRadius: "8px",
        }}
      >
        <LoaderSpinner fullpage={false} />
      </div>}
      <div className={styles.dFlex}>
        <div className={styles.section} style={{ width: "35%" }}>
          <div className={styles.targetGraphic}>
            <div className={styles.sourceDiagramContainer}>
              {selectedSource ? (
                <div>
                  <div className={styles.selectedSourceLogoOverlay}>
                    <img
                      src={selectedSource.imgUrl || "/placeholder.svg"}
                      alt={selectedSource.displayName}
                      className={styles.selectedSourceLogo}
                    />
                  </div>
                  <img src="/assets/Source-desktop.png" alt="Source Diagram" className={styles.sourceBlankImage} />
                </div>
              ) : (
                <img src="/assets/Sourceconnector.png" alt="Source Diagram" className={styles.sourceBlankImage} />
              )}
            </div>
          </div>
        </div>
        <div className={styles.section} style={{ width: "65%" }}>
          <div className={styles.sourceContainer}>          <div className={styles.sourceHeaderContainer}>
            <h3 className={styles.sourceTitle}>SELECT A SOURCE</h3>
            <div
              className={styles.sourceHelpContainer}
              onClick={() => {
                displayArticle("What source platforms are supported?")
              }}
              style={{ cursor: "pointer" }}
            >
              <img src="/assets/help.png" alt="Help" className={globalStyles.helpIcon} />
              <span style={{ fontFamily: "Inter", fontSize: "14px", color: "#746B68", letterSpacing: "-0.2px" }}>What source platforms are supported?</span>
            </div>
          </div>

            <div className={styles.sourceOptions}>
              <div>
                <button
                  className={globalStyles.mainButton}
                  style={
                    selectedFile || selectedSource
                      ? {
                        width: "100%",
                        marginTop: "20px",
                        backgroundColor: "#FFFFFF",
                        color: "#514742",
                        border: "1px solid #DCDAD9",
                        boxShadow: "2px 2px 5px 0 rgba(0, 0, 0, 0.10)",
                      }
                      : { width: "100%" }
                  }
                  onClick={() => setShowModal(true)}
                >
                  {selectedSource ? "Reselect Source Platform" : "Select a source platform"}
                </button>

                {sourceType === "csv" && (
                  <>
                    <div
                      style={{
                        display: "flex",
                        alignItems: "center",
                        marginLeft: "auto",
                        marginTop: "10px",
                        cursor: "pointer",
                      }}
                      onClick={() => {
                        displayArticle("Connecting to CSV as a Source")
                      }}
                    >
                      <img src="/assets/help.png" alt="Help" className={globalStyles.helpIcon} />
                      <span className={globalStyles.guideName}>Connecting to CSV as a Source</span>
                    </div>
                    {/* <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                      marginLeft: "auto",
                      marginTop: "10px",
                      cursor: "pointer"
                    }}
                    onClick={() => {
                      displayArticle("CSV File to Freshservice Data Preparation Steps");
                    }}
                  >
                    <img src="/assets/help.png" alt="Help" className={globalStyles.helpIcon} />
                    <span className={globalStyles.guideName}>CSV File to Freshservice Data Preparation Steps</span>
                  </div> */}
                  </>
                )}
              </div>
            </div>

            {selectedFile && (
              <div
                className={styles.selectedFileContainer}
                style={{
                  backgroundColor: "#fff",
                  padding: "15px 20px",
                  borderRadius: "100px",
                  marginTop: "20px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                  boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
                }}
              >
                <div style={{ display: "flex", alignItems: "center", gap: "10px" }}>
                  <i className="fa-solid fa-file"></i>
                  <span className={globalStyles.interSummaryStyle}>{selectedFile.name}</span>
                </div>
                <div style={{ width: "65px" }}></div>
                <button
                  onClick={removeSelectedFile}
                  style={{
                    background: "none",
                    border: "none",
                    cursor: "pointer",
                    fontSize: "16px",
                  }}
                >
                  <i className="fa fa-trash" aria-hidden="true"></i>
                </button>
              </div>
            )}
          </div>

          <Dialog
            open={showModal}
            onClose={() => setShowModal(false)}
            PaperProps={{
              sx: { width: "705px", maxWidth: "none", backgroundColor: "#170903", marginBottom: "20px" },
            }}
          >
            <DialogContent sx={{ backgroundColor: "#170903", padding: "0", marginBottom: "30px", marginTop: "5px" }}>
              <SourceTargetSelector
                showModel={showModal}
                setShowModel={setShowModal}
                data={sourceData
                  .filter((source) => source.isSource)
                  .map((source) => ({
                    src: source.imgUrl,
                    name: source.displayName,
                    value: source.name,
                  }))}
                name="SOURCE"
                onSelect={handleSourceSelect}
              />
            </DialogContent>
          </Dialog>

          <Dialog
            open={showKeyDialog}
            onClose={() => setShowKeyDialog(false)}
            PaperProps={{
              sx: { width: "400px", padding: "20px", overflow: "hidden !important" },
            }}
          >
            <DialogContent sx={{ overflow: "hidden" }}>
              <div style={{ display: "flex", flexDirection: "column", gap: "15px" }}>
                <h3 style={{ margin: 0, fontSize: "16px", fontFamily: "inter" }}>Enter the key name</h3>
                <input
                  style={{ width: "80%" }}
                  className={`form-control ${styles.standardInput}`}
                  type="text"
                  value={newKeyName}
                  onChange={(e) => setNewKeyName(e.target.value)}
                  placeholder="Enter key name"
                />
                <div style={{ display: "flex", justifyContent: "flex-end", gap: "10px", marginTop: "10px" }}>
                  <button
                    onClick={() => setShowKeyDialog(false)}
                    style={{
                      padding: "8px 16px",
                      borderRadius: "4px",
                      border: "1px solid #ccc",
                      background: "white",
                      cursor: "pointer",
                    }}
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleKeyDialogSubmit}
                    style={{
                      padding: "8px 16px",
                      borderRadius: "4px",
                      border: "none",
                      background: "#EF8963",
                      color: "#170903",
                      cursor: "pointer",
                      fontWeight: "500",
                    }}
                  >
                    OK
                  </button>
                </div>
              </div>
            </DialogContent>
          </Dialog>

          <Dialog
            open={showUploadDialog}
            onClose={() => setShowUploadDialog(false)}
            maxWidth="md"
            PaperProps={{
              sx: { backgroundColor: "transparent", boxShadow: "none", overflow: "visible" },
            }}
          >
            <DialogContent sx={{ padding: 0, overflow: "visible" }}>
              <UploadCSV onFileUploaded={handleCsvUploaded} onClose={() => setShowUploadDialog(false)} />
            </DialogContent>
          </Dialog>

          {selectedSource && sourceType !== "csv" && (
            <div className={styles.formSection} style={{ marginTop: "35px" }}>
              <div className={styles.title}>CONNECT TO A {selectedSource.displayName.toUpperCase()} SOURCE PROFILE</div>
              <div className={styles.helpRow} style={{ marginTop: "15px" }}>
                <span>Enter the details</span>
                <div
                  style={{ marginLeft: "auto", display: "flex", alignItems: "center", gap: "8px", cursor: "pointer" }}
                  onClick={() => displayArticle(`What is a ${selectedSource.displayName} Instance URL?`)}
                >
                  <img src="/assets/help.png" alt="Help" className={styles.helpIcon} />
                  <span className={styles.helpText}>{`What is a ${selectedSource.displayName} Instance URL?`}</span>
                </div>
              </div>

              <form onSubmit={handleConnect}>
                <div>
                  {selectedSource?.fields?.map((field) => (
                    <div key={field.name} className={styles.inputContainer}>
                      <input
                        className={`${styles.formControl} ${field.type === "password" ? styles.passwordInput : ""}`}
                        type={field.type === "password" && visibleFields[field.name] ? "text" : field.type}
                        placeholder={field.placeholder ? `${field.placeholder}*` : "Enter value*"}
                        value={formData[field.name] || ""}
                        onChange={(e) => handleInputChange(e, field.name)}
                      />

                      {field.type === "password" && (
                        <span className={styles.eyeIconContainer} onClick={() => toggleVisibility(field.name)}>
                          {visibleFields[field.name] ? (
                            <EyeIcon className={styles.eyeIcon} />
                          ) : (
                            <EyeOffIcon className={styles.eyeIcon} />
                          )}
                        </span>
                      )}
                    </div>
                  ))}
                </div>

                <div style={{ gap: "10px", marginTop: "10px" }}>
                  {connectionStatus === "success" ? (
                    <div className={styles.successContainer} style={{ width: "100%" }}>
                      <button className={globalStyles.connectedSuccess} style={{ width: "100%", marginTop: "20px" }}>
                        Connected Successfully!
                        <CheckIcon
                          className="eye-icon"
                          style={{
                            color: "green",
                            marginLeft: "10px",
                          }}
                        />
                      </button>

                      <div
                        style={{
                          display: "flex",
                          alignItems: "center",
                          marginLeft: "auto",
                        }}
                        onClick={() => {
                          displayArticle("What are query parameters?")
                        }}
                      >
                        <img src="/assets/help.png" alt="Help" className={globalStyles.helpIcon} />
                        <span className={globalStyles.guideName}>What are query parameters?</span>
                      </div>

                      <div className={styles.queryParamSection} >
                        <div className={styles.queryParamHeader}  >
                          <div style={{ display: "flex", alignItems: "center" }}>
                            <span className={styles.queryText}>Do you want to add query parameters?</span>
                            {checked && (
                              <button
                                onClick={addCustomQueryParam}
                                style={{
                                  border: "1px solid #EA5822",
                                  borderRadius: "4px",
                                  padding: "4px 12px",
                                  background: "white",
                                  color: "#EA5822",
                                  cursor: "pointer",
                                  marginLeft: "10px",
                                  fontSize: "12px",
                                  fontWeight: "bold",
                                }}
                              >
                                Add
                              </button>
                            )}
                          </div>
                          <FormControlLabel
                            control={
                              <Switch
                                checked={checked}
                                onChange={() => setChecked(!checked)}
                                sx={{
                                  width: 50,
                                  height: 25,
                                  padding: 0,
                                  "& .MuiSwitch-switchBase": {
                                    padding: 0,
                                    margin: "3px 5px",
                                    transitionDuration: "300ms",
                                    "&.Mui-checked": {
                                      transform: "translateX(25px)",
                                      color: "#fff",
                                      "& + .MuiSwitch-track": {
                                        backgroundColor: "#E97451",
                                        opacity: 1,
                                        border: 0,
                                      },
                                      "& .MuiSwitch-thumb": {
                                        backgroundColor: "#fff",
                                        width: 20,
                                        height: 20,
                                      },
                                      "&.Mui-disabled + .MuiSwitch-track": {
                                        opacity: 0.5,
                                      },
                                    },
                                  },
                                  "& .MuiSwitch-thumb": {
                                    backgroundColor: "#fff",
                                    boxSizing: "border-box",
                                    width: 18,
                                    height: 18,
                                    borderRadius: "50%",
                                    transition: "width 0.2s, height 0.2s",
                                  },
                                  "& .MuiSwitch-track": {
                                    borderRadius: 12,
                                    backgroundColor: "#B9B5B3",
                                    opacity: 1,
                                    transition: "background-color 0.5s",
                                  },
                                }}
                              />
                            }
                          />
                        </div>
                        {checked && (
                          <div
                            className={styles.inputContainer}
                            style={{
                              display: "flex",
                              flexDirection: "column",
                              width: "100%",
                              gap: "8px",
                              marginTop: "15px",
                            }}
                          >
                            {queryParamData.map(
                              (param, index) =>
                                !deletedCustomKeys.includes(param.key) && (
                                  <div
                                    key={index}
                                    style={{ position: "relative", display: "flex", alignItems: "center" }}
                                  >
                                    <input
                                      className={`form-control ${styles.standardInput}`}
                                      type="text"
                                      placeholder={param.placeholder || param.key}
                                      value={queryFormData?.[param.key] || ""}
                                      onChange={(e) => handleQueryInputChange(e, param.key)}
                                      style={{ flex: 1, marginTop: "8px" }}
                                    />
                                    {param.req === false && (
                                      <button
                                        onClick={() => removeQueryParam(param.key)}
                                        style={{
                                          position: "absolute",
                                          right: "10px",
                                          background: "none",
                                          border: "none",
                                          cursor: "pointer",
                                          display: "flex",
                                          alignItems: "center",
                                        }}
                                        type="button"
                                      >
                                        <MinusIcon style={{ width: "20px", height: "20px", color: "#EA5822" }} />
                                      </button>
                                    )}
                                  </div>
                                ),
                            )}

                            {customQueryParams.map((param, index) => (
                              <div
                                key={`custom-${index}`}
                                style={{ position: "relative", display: "flex", alignItems: "center" }}
                              >
                                <input
                                  className={`form-control ${styles.standardInput}`}
                                  type="text"
                                  placeholder={param.key}
                                  value={param.value || ""}
                                  onChange={(e) => handleCustomQueryInputChange(e, index)}
                                  style={{ flex: 1, marginTop: "15px" }}
                                />
                                <button
                                  onClick={() => removeCustomQueryParam(index)}
                                  style={{
                                    position: "absolute",
                                    right: "10px",
                                    background: "none",
                                    border: "none",
                                    cursor: "pointer",
                                    display: "flex",
                                    alignItems: "center",
                                  }}
                                  type="button"
                                >
                                  <MinusIcon style={{ width: "20px", height: "20px", color: "#EA5822" }} />
                                </button>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>

                      <div style={{ display: "flex", gap: "10px", marginTop: "20px", marginBottom: "30px" }}>
                        <button
                          className={styles.resetButton}
                          onClick={handleReset}
                          type="button"
                          style={{
                            padding: "10px 20px",
                            borderRadius: "4px",
                            border: "1px solid #EA5822",
                            background: "white",
                            color: "#EA5822",
                            cursor: "pointer",
                            fontWeight: "500",
                            flex: "1",
                          }}
                        >
                          Reset
                        </button>
                        <button
                          className={styles.newConfirmButton}
                          onClick={moveToNextStep}
                          type="button"
                          style={{ flex: "3" }}
                        >
                          Confirm & Continue
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div style={{ display: "flex", gap: "10px", marginTop: "20px", marginBottom: "30px" }}>
                      <button
                        type="button"
                        className={styles.resetButton}
                        style={{
                          padding: "10px 20px",
                          borderRadius: "4px",
                          border: "1px solid #EA5822",
                          background: "white",
                          color: "#EA5822",
                          cursor: "pointer",
                          fontWeight: "500",
                          flex: "1",
                        }}
                        onClick={handleReset}
                      >
                        Reset
                      </button>
                      <button
                        type="button"
                        className={globalStyles.mainButton}
                        style={{ flex: "3", marginTop: "0", width: "90%" }}
                        onClick={validateSourceFun}
                        disabled={isLoading}
                      >
                        {isLoading ? (
                          <div className={styles.loaderContainer}>
                            <div className={styles.loader}></div>
                            <span>Connecting...</span>
                          </div>
                        ) : connectionStatus === "failed" ? (
                          "Reconnect"
                        ) : (
                          "Connect"
                        )}
                      </button>
                    </div>
                  )}
                </div>
              </form>
              {/* {openDialog && (
              <Dialog open={openDialog} onClose={() => setOpenDialog(false)}>
                <DialogContent sx={{ backgroundColor: "#170903", padding: "60px 100px" }}>
                  <span className={globalStyles.selectionName} style={{ fontSize: "16px" }}>
                    Connected to {selectedSource.name} profile!
                  </span>
                </DialogContent>
              </Dialog>
            )} */}
            </div>
          )}
          <ToastContainer />

          {sourceType === "csv" && (
            <div className={styles.formSection} style={{ marginTop: "35px" }}>
              {/* <div className={styles.title}>CSV FILE SELECTED</div> */}
              <button className={styles.newConfirmButton} style={{ width: "100%" }} onClick={moveToNextStep}>
                Confirm & Continue
              </button>
              {/* {openDialog && (
              <Dialog open={openDialog} onClose={() => setOpenDialog(false)}>
                <DialogContent sx={{ backgroundColor: "#170903", padding: "60px 100px" }}>
                  <span className={globalStyles.selectionName} style={{ fontSize: "16px" }}>
                    Source CSV is selected
                  </span>
                </DialogContent>
              </Dialog>
            )} */}
            </div>
          )}
        </div>
      </div>
    </>
  )
}

export default SourceSelect
