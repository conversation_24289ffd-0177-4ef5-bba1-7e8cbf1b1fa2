import { useState, useEffect } from "react"
import styles from "./ProductPricing.module.css"
import Sidebar from "../Sidebar/Sidebar"
import globalStyles from "../globalStyles.module.css"
import { GlobeIcon, SearchIcon } from "@heroicons/react/solid"
import { useNavigate } from "react-router-dom"
import BookDemo from "../Book-Demo/BookDemo"
import { Dialog, DialogContent } from "@mui/material"
import config from '../../Config/config.json';

const ProductPricing = () => {
  const [formValues, setFormValues] = useState({
    tickets: 0,
    notesPerTicket: 0,         // was ticketNotes
    kbArticles: 0,
    otherRecords: 0,
    notesPerOtherRecord: 0,    // was otherDeps
    changeTickets: 0,
    notesPerChangeTicket: 0,   // was changeDeps
    problemTickets: 0,
    notesPerProblemTicket: 0,  // was problemDeps
    projectTickets: 0,
    notesPerProjectTicket: 0,  // was projectDeps
  })
  const [totalRecords, setTotalRecords] = useState(0)
  const [price, setPrice] = useState(0)

  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormValues((prev) => ({
      ...prev,
      [name]: Number(value),
    }))
  }
  const trustedLogos = config.TRUSTED_BY_LOGOS
  const calculateRecordsAndPrice = () => {
    const {
      tickets,
      notesPerTicket,         // formerly ticketNotes
      kbArticles,
      otherRecords,
      notesPerOtherRecord,     // formerly otherDeps
      changeTickets,
      notesPerChangeTicket,    // formerly changeDeps
      problemTickets,
      notesPerProblemTicket,   // formerly problemDeps
      projectTickets,
      notesPerProjectTicket,   // formerly projectDeps
    } = formValues

    const plan = (selectedPlan || "pro").toLowerCase()

    const total =
      tickets * (1 + notesPerTicket) +
      kbArticles +
      otherRecords * (1 + notesPerOtherRecord) +
      changeTickets * (1 + notesPerChangeTicket) +
      problemTickets * (1 + notesPerProblemTicket) +
      projectTickets * (1 + notesPerProjectTicket)

    // cap at 1,000,000 - Note needed
    //const total = Math.min(rawTotal, 1000000)

    setTotalRecords(total)

    if (total === 0) {
      setPrice(0)
      return
    }

    if (total > 10_000 && total < 1_000_000) {
      setSelectedPlan("pro")   // correctly update the plan
    }

    if (total > 1_000_000) {
      setSelectedPlan("whiteglove")   // correctly update the plan
      setPrice(0)
      return
    }

    const pricingTiers = [
      { limit: 1000, standard: 900, discounted: 750 },
      { limit: 5000, standard: 1500, discounted: 1250 },
      { limit: 10000, standard: 2100, discounted: 1750 },
      { limit: 30000, standard: 3300, discounted: 2750 },
      { limit: 50000, standard: 4200, discounted: 3500 },
      { limit: 100000, standard: 5700, discounted: 4750 },
      { limit: 250000, standard: 7800, discounted: 6500 },
      { limit: 500000, standard: 10200, discounted: 8500 },
      { limit: 1000000, standard: 14100, discounted: 11750 },
      { limit: 2500000, standard: 22500, discounted: 18750 },
      { limit: 5000000, standard: 30000, discounted: 25000 },
      { limit: 20000000, standard: 63000, discounted: 52500 },
    ]

    const tier = pricingTiers.find((t) => total <= t.limit) || pricingTiers[pricingTiers.length - 1]
    const finalPrice = plan === "essential" ? tier.discounted : tier.standard
    setPrice(finalPrice)
  }

  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(() => {
    return localStorage.getItem("isSidebarCollapsed") === "true"
  })
  const [openDemo, setOpenDemo] = useState(false)
  const [selectedPlan, setSelectedPlan] = useState("pro")
  useEffect(() => {
    // whenever the plan changes, recalc
    calculateRecordsAndPrice()
  }, [selectedPlan, formValues])

  const navigate = useNavigate()
  const handleStartMigration = () => {
    // Force clear all localStorage data before starting new migration
    const keysToRemove = [
      'completedSteps',
      'lastActiveStep',
      'migrationState',
      'sourceConnectionData',
      'targetConnectionData',
      'templateState',
      'planData',
      'migrationProgress',
      'migrationId'
    ];
    keysToRemove.forEach(key => {
      localStorage.removeItem(key);
    });

    // Navigate to data migration with newTemplate flag to force fresh start
    navigate("/data-migration", {
      state: {
        newTemplate: true,
        clearAll: true
      },
      replace: true // Replace current history entry to prevent back navigation to stale state
    });

    // Force reload the page to ensure clean slate
    window.location.reload();
  }

  const handleBookDemo = () => {
    setOpenDemo(true)
  }

  const closeDemo = () => {
    setOpenDemo(false)
  }

  const handlePlanSelect = (plan) => {
    setSelectedPlan(plan)
  }

  return (
    <div className={styles.container}>
      <Sidebar isCollapsed={isSidebarCollapsed} setIsCollapsed={setIsSidebarCollapsed} />

      <div className={`${styles.mainSection} ${isSidebarCollapsed ? styles.expanded : ""}`}>
        <div className={styles.contentSection}>
          <div className={styles.heroSection}>
            <div className={styles.heroHeader}>
              {/* <h1 className={globalStyles.headerStyle} style={{padding: "0"}}>Product & Pricing</h1> */}
              <div className={styles.headerControls}>
                {/* <div className={globalStyles.searchWrapper}>
                  <SearchIcon className={globalStyles.searchIcon} />
                  <input
                    type="text"
                    placeholder="Search..."
                    className={globalStyles.searchInput}
                    onFocus={(e) => {
                      e.target.style.width = "200px"
                      e.target.placeholder = "Typing..."
                    }}
                    onBlur={(e) => {
                      e.target.style.width = "80px"
                      e.target.placeholder = "Search..."
                    }}
                  />
                </div> */}

                <div className={globalStyles.searchWrapper} >
                  <GlobeIcon className={globalStyles.searchIcon} />
                  <input
                    type="text"
                    placeholder="Eng"
                    className={`${globalStyles.searchInput} ${globalStyles.languageSelector}`}
                    readOnly
                  />
                </div>
              </div>
            </div>

            <div className={styles.heroContent}>
              <div className={styles.broughtBySection}>
                <a
                  href="https://www.saasgenie.ai/"
                  className={styles.saasGenieLink}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Brought to you by saasgenie ↗
                </a>
              </div>



              <h2 className={styles.mainTitle}>Effortless data migration across all platforms</h2>
              <p className={styles.mainDescription}>
                migrateGenie simplifies secure data migration between platforms, automating mapping, reducing errors,
                and ensuring a seamless transition.
              </p>
              <button className={styles.startButton} onClick={handleStartMigration}>
                Start a migration
              </button>
            </div>
            <div className={styles.heroImage}>
              <img src="/assets/image.png" alt="Migration Illustration" />
            </div>
          </div>

          <div className={styles.featuresLayout}>
            <div className={styles.iconsSection}>
              <div className={styles.iconWrapper}>
                <div className={styles.analyticsIcon}>
                  <img src="/assets/analytics.png" alt="Analytics" />
                </div>
                <div className={styles.nanoIcon}>
                  <img src="/assets/nano.png" alt="Nano" />
                </div>
                <div className={styles.checkIcon}>
                  <img src="/assets/easy.png" alt="Easy" />
                </div>
                <div className={styles.lockIcon}>
                  <img src="/assets/lock.png" alt="Security" />
                </div>
              </div>
            </div>

            <div className={styles.keyFeaturesSection}>
              <h3 className={styles.keyFeaturesTitle}>KEY FEATURES</h3>

              <div className={styles.featureItem}>
                <h4>Effortless and user-friendly</h4>
                <p>Quick, automated transfers with minimal manual effort.</p>
              </div>

              <div className={styles.featureItem}>
                <h4>Smart AI-powered solutions</h4>
                <p>AI-powered mapping for accurate data transformation.</p>
              </div>

              <div className={styles.featureItem}>
                <h4>Data security & privacy</h4>
                <p>End-to-end encryption ensures data safety.</p>
              </div>

              <div className={styles.featureItem}>
                <h4>Detailed reports & metrics</h4>
                <p>Monitor progress with live updates and reports.</p>
              </div>
            </div>
          </div>

          <div className={styles.pricingSection}>
            <h2 className={styles.pricingTitle}>Packages</h2>
            <p className={styles.pricingDescription}>
              migrateGenie offers flexible pricing plans—Essential, Pro, and White-Glove—designed to fit your migration
              needs. Choose the right package for seamless, secure, and efficient data transfers.
            </p>
            <button className={styles.demoButton} onClick={handleBookDemo}>
              Book a demo
            </button>
          </div>

          <div className={styles.pricingTablesContainer}>
            {/* Essential Migration Table */}
            <div
              className={`${styles.pricingTableContainer} ${selectedPlan === "essential" ? styles.selectedPlan : ""}`}
              onClick={() => handlePlanSelect("essential")}
            >
              <div className={styles.pricingTableHeader}>
                <h3>ESSENTIAL MIGRATION</h3>
                <h3>(10,000 Records)</h3>
              </div>
              <div className={styles.pricingTableContent}>
                <div className={styles.pricingTableRow}>
                  <p><b>All Basic migration features included in this package:</b></p>
                </div>
                <div className={styles.pricingTableRow}>
                  <p>Up to Ten Thousand Records</p>
                </div>
                <div className={styles.pricingTableRow}>
                  <p>Up to Two migration templates</p>
                </div>
                <div className={styles.pricingTableRow}>
                  <p>One Sample Migration per template</p>
                </div>
                <div className={styles.pricingTableRow}>
                  <p>Support - Weekdays 8 hours X 5 days (US EST)</p>
                </div>
                <div className={styles.pricingTableRow}>
                  <p>Support via Email only</p>
                </div>
              </div>
            </div>

            {/* Pro Migration Table */}
            <div
              className={`${styles.pricingTableContainer} ${selectedPlan === "pro" ? styles.selectedPlan : ""}`}
              onClick={() => handlePlanSelect("pro")}
            >
              <div className={styles.pricingTableHeader}>
                <h3>PRO MIGRATION</h3>
                <h3>(1 Million Records)</h3>
              </div>
              <div className={styles.pricingTableContent}>
                <div className={styles.pricingTableRow}>
                  <p><b>All ESSENTIAL migration features included along with the following:</b></p>
                </div>
                <div className={styles.pricingTableRow}>
                  <p>Up to 1 Million Records</p>
                </div>
                <div className={styles.pricingTableRow}>
                  <p>Three additional migration templates</p>
                </div>
                <div className={styles.pricingTableRow}>
                  <p>Two additional Sample Migrations per template</p>
                </div>
                <div className={styles.pricingTableRow}>
                  <p>Support - Weekdays 8 AM to 8 PM (US EST) & Weekends - 9 AM to 5 PM (US EST)</p>
                </div>
                <div className={styles.pricingTableRow}>
                  <p>Support via Chat</p>
                </div>
                <div className={styles.pricingTableRow}>
                  <p>Assigned migration engineer allocated to your account</p>
                </div>
                <div className={styles.pricingTableRow}>
                  <p>Custom mapping options</p>
                </div>
                <div className={styles.pricingTableRow}>
                  <p>Reports</p>
                </div>
              </div>
            </div>

            {/* White Glove Migration Table */}
            <div
              className={`${styles.pricingTableContainer} ${selectedPlan === "whiteglove" ? styles.selectedPlan : ""}`}
              onClick={() => handlePlanSelect("whiteglove")}
            >
              <div className={styles.pricingTableHeader}>
                <h3>WHITEGLOVE MIGRATION</h3>
                <h3>(More than 1 Million Records)</h3>
              </div>
              <div className={styles.pricingTableContent}>
                <div className={styles.pricingTableRow}>
                  <p><b>All ESSENTIAL & PRO migration features included along with the following:</b></p>
                </div>
                <div className={styles.pricingTableRow}>
                  <p>More than 1 Million Records</p>
                </div>
                <div className={styles.pricingTableRow}>
                  <p>Unlimited migration templates</p>
                </div>
                <div className={styles.pricingTableRow}>
                  <p>Unlimited Sample migrations</p>
                </div>
                <div className={styles.pricingTableRow}>
                  <p>24/7 support</p>
                </div>
                <div className={styles.pricingTableRow}>
                  <p>Fully managed migration services with a dedicated Engagement Manager & Migration Engineer</p>
                </div>
                <div className={styles.pricingTableRow}>
                  <p>End to end handling of account</p>
                </div>
              </div>
            </div>
          </div>

          <div className={styles.calculatorContainer}>
            {/* Fixed header */}
            <div className={styles.calculatorHeader}>
              {/*
    <button
      onClick={calculateRecordsAndPrice}
      className={styles.calculateButton}>
      CALCULATE PRICING
    </button>
    */}
              <div className={styles.resultsBox}>
                <h2 className={styles.pricingTitle}>Migration Pricing Calculator</h2>
                <p className={styles.totalRecords}>
                  <strong>Total Records:</strong> {totalRecords.toLocaleString()}
                </p>
                <p className={styles.estimatedPrice}>
                  <strong>
                    Estimated Pricing{" "}
                    {selectedPlan?.toUpperCase() === "WHITEGLOVE"
                      ? "(WHITEGLOVE)"
                      : `(${selectedPlan?.toUpperCase()})`}
                    :
                  </strong>{" "}
                  {selectedPlan?.toUpperCase() === "WHITEGLOVE"
                    ? "Contact Us for Pricing"
                    : `$${price.toLocaleString()}`}
                </p>
              </div>
            </div>

            {/* Scrollable questions */}
            <div className={styles.calculatorBody}>
              <p className={styles.pricingDescription}>
                <strong>
                  Estimate your record count and migration price based on your data.
                </strong>
              </p>

              {/* Question 1 */}
              <div className={styles.calculatorSection}>
                <label className={styles.calculatorLabel}>
                  1. How many tickets (including incidents and service requests) would you
                  like to migrate?
                </label>
                <input
                  type="number"
                  name="tickets"
                  value={formValues.tickets}
                  onChange={handleInputChange}
                  onFocus={e => e.target.select()}
                  className={styles.calculatorInput}
                />
              </div>
              <div className={styles.calculatorSubtext}>
                (We typically see around 10 per ticket, but this may vary for your
                business.)
              </div>

              {/* Question 2 */}
              <div className={styles.calculatorSection}>
                <label className={styles.calculatorLabel}>
                  2. On average, how many notes and tasks are associated with each
                  ticket?
                </label>
                <input
                  type="number"
                  name="notesPerTicket"
                  value={formValues.notesPerTicket}
                  onChange={handleInputChange}
                  onFocus={e => e.target.select()}
                  className={styles.calculatorInput}
                />
              </div>

              {/* Question 3 */}
              <div className={styles.calculatorSection}>
                <label className={styles.calculatorLabel}>
                  3. How many knowledge base articles need to be migrated?
                </label>
                <input
                  type="number"
                  name="kbArticles"
                  value={formValues.kbArticles}
                  onChange={handleInputChange}
                  onFocus={e => e.target.select()}
                  className={styles.calculatorInput}
                />
              </div>

              {/* Question 4 */}
              <div className={styles.calculatorSection}>
                <label className={styles.calculatorLabel}>
                  4. Are there any other types of records you’d like to migrate
                  (e.g., contacts, deals)?
                </label>
                <input
                  type="number"
                  name="otherRecords"
                  value={formValues.otherRecords}
                  onChange={handleInputChange}
                  onFocus={e => e.target.select()}
                  className={styles.calculatorInput}
                />
              </div>

              {/* Question 5 */}
              <div className={styles.calculatorSection}>
                <label className={styles.calculatorLabel}>
                  5. Do these records have dependent records (e.g., notes or tasks)? If
                  so, how many on average per record?
                </label>
                <input
                  type="number"
                  name="notesPerOtherRecord"
                  value={formValues.notesPerOtherRecord}
                  onChange={handleInputChange}
                  onFocus={e => e.target.select()}
                  className={styles.calculatorInput}
                />
              </div>

              <p className={styles.pricingDescription}>
                <strong>
                  Only for ITSM Platforms (e.g., ServiceNow, Jira Service Management)
                </strong>
              </p>

              {/* Question 6 */}
              <div className={styles.calculatorSection}>
                <label className={styles.calculatorLabel}>
                  6. How many change tickets would you like to migrate?
                </label>
                <input
                  type="number"
                  name="changeTickets"
                  value={formValues.changeTickets}
                  onChange={handleInputChange}
                  onFocus={e => e.target.select()}
                  className={styles.calculatorInput}
                />
              </div>

              {/* Question 7 */}
              <div className={styles.calculatorSection}>
                <label className={styles.calculatorLabel}>
                  7. On average, how many notes, tasks, and approvals are associated
                  with each change ticket?
                </label>
                <input
                  type="number"
                  name="notesPerChangeTicket"
                  value={formValues.notesPerChangeTicket}
                  onChange={handleInputChange}
                  onFocus={e => e.target.select()}
                  className={styles.calculatorInput}
                />
                <div className={styles.calculatorSubtext}>
                  (We typically see about 15 per ticket.)
                </div>
              </div>

              {/* Question 8 */}
              <div className={styles.calculatorSection}>
                <label className={styles.calculatorLabel}>
                  8. How many problem tickets would you like to migrate?
                </label>
                <input
                  type="number"
                  name="problemTickets"
                  value={formValues.problemTickets}
                  onChange={handleInputChange}
                  onFocus={e => e.target.select()}
                  className={styles.calculatorInput}
                />
              </div>

              {/* Question 9 */}
              <div className={styles.calculatorSection}>
                <label className={styles.calculatorLabel}>
                  9. On average, how many notes and tasks are associated with each
                  problem ticket?
                </label>
                <input
                  type="number"
                  name="notesPerProblemTicket"
                  value={formValues.notesPerProblemTicket}
                  onChange={handleInputChange}
                  onFocus={e => e.target.select()}
                  className={styles.calculatorInput}
                />
                <div className={styles.calculatorSubtext}>
                  (Typically ~5 per ticket.)
                </div>
              </div>

              {/* Question 10 */}
              <div className={styles.calculatorSection}>
                <label className={styles.calculatorLabel}>
                  10. How many project tickets would you like to migrate?
                </label>
                <input
                  type="number"
                  name="projectTickets"
                  value={formValues.projectTickets}
                  onChange={handleInputChange}
                  onFocus={e => e.target.select()}
                  className={styles.calculatorInput}
                />
              </div>

              {/* Question 11 */}
              <div className={styles.calculatorSection}>
                <label className={styles.calculatorLabel}>
                  11. On average, how many notes and tasks are associated with each
                  project ticket?
                </label>
                <input
                  type="number"
                  name="notesPerProjectTicket"
                  value={formValues.notesPerProjectTicket}
                  onChange={handleInputChange}
                  onFocus={e => e.target.select()}
                  className={styles.calculatorInput}
                />
                <div className={styles.calculatorSubtext}>
                  (Typically ~5 per ticket.)
                </div>
              </div>
            </div>
          </div>




          <div className={styles.trustedSection}>
            <div className={styles.trustedHeader}>
              <img src="/assets/saasgenie_logo.png" alt="saasgenie Logo" className={styles.trustedLogo} />
              <h3 className={styles.trustedTitle}>Trusted by industry leaders world-wide</h3>
            </div>
            <div className={globalStyles.trustedLogos}>
              {trustedLogos.map((logo, idx) =>
                logo.url ? (
                  <a
                    key={logo.alt}
                    href={logo.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className={styles.trustedLogo}
                    style={{ animationDelay: `${0.1 * (idx + 1)}s` }}
                  >
                    <img
                      src={logo.src}
                      alt={logo.alt}
                      style={{ width: 60, height: 50, objectFit: "contain" }}
                    />
                  </a>
                ) : (
                  <div
                    key={logo.alt}
                    className={styles.trustedLogo}
                    style={{ animationDelay: `${0.1 * (idx + 1)}s` }}
                  >
                    <img
                      src={logo.src}
                      alt={logo.alt}
                      style={{ width: 60, height: 50, objectFit: "contain" }}
                    />
                  </div>
                )
              )}
            </div>
          </div>
        </div>
      </div>

      <Dialog
        open={openDemo}
        maxWidth="md"
        PaperProps={{
          style: {
            width: "700px",
          },
        }}
      >
        <DialogContent style={{ padding: "0", overflow: "auto", backgroundColor: "#170903" }}>
          <BookDemo close={closeDemo} />
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default ProductPricing

