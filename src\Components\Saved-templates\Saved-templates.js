"use client"

import { useState, useEffect } from "react"
import styles from "./Saved-templates.module.css"
import Sidebar from "../Sidebar/Sidebar"
import { HiOutlineFunnel } from "react-icons/hi2"
import globalStyles from "../globalStyles.module.css"
import { GlobeIcon, SearchIcon, ChevronDownIcon } from "@heroicons/react/solid"
import { deleteplan, getMigrationPlans } from "../apiService"
import { useNavigate } from "react-router-dom"
import LoaderSpinner from "../loaderspinner"

const textStyle = {
  color: "#170903",
  fontFamily: "Inter",
  fontSize: "14px",
  fontStyle: "normal",
  fontWeight: 400,
}

const sourcePlatforms = [
  <div key="zendesk" style={textStyle}>
    Zendesk
  </div>,
  <div key="freshservice" style={textStyle}>
    Freshservice
  </div>,
  <div key="servicenow" style={textStyle}>
    ServiceNow
  </div>,
  <div key="salesforce" style={textStyle}>
    Salesforce
  </div>,
  <div key="csv" style={textStyle}>
    CSV
  </div>,
  <div key="others" style={textStyle}>
    Others
  </div>,
]

const targetPlatforms = [
  <div key="zendesk" style={textStyle}>
    Zendesk
  </div>,
  <div key="freshservice" style={textStyle}>
    Freshservice
  </div>,
  <div key="servicenow" style={textStyle}>
    ServiceNow
  </div>,
  <div key="salesforce" style={textStyle}>
    Salesforce
  </div>,
  <div key="csv" style={textStyle}>
    CSV
  </div>,
  <div key="others" style={textStyle}>
    Others
  </div>,
]

const SavedTemplates = () => {
  const navigate = useNavigate()

  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(() => {
    return localStorage.getItem("isSidebarCollapsed") === "true"
  })
  const [email, setEmail] = useState(localStorage.getItem("email"))
  const [showFilterDropdown, setShowFilterDropdown] = useState(false)
  const [filterType, setFilterType] = useState("all")
  const [selectedSourcePlatform, setSelectedSourcePlatform] = useState(null)
  const [selectedTargetPlatform, setSelectedTargetPlatform] = useState(null)
  const [showSourcePlatformDropdown, setShowSourcePlatformDropdown] = useState(false)
  const [showTargetPlatformDropdown, setShowTargetPlatformDropdown] = useState(false)
  const [templates, setTemplates] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [filteredTemplates, setFilteredTemplates] = useState([])
  const [rawApiResponse, setRawApiResponse] = useState(null)
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [templateToDelete, setTemplateToDelete] = useState(null)
  const [expandedTimelines, setExpandedTimelines] = useState({})
  const [deleteLoading, setDeleteLoading] = useState(false)
  const [templateUsedCount, setTemplateUsedCount] = useState(0)

  // Pagination states
  const [currentPage, setCurrentPage] = useState(0)
  const [rowsPerPage, setRowsPerPage] = useState(25)
  const [paginatedData, setPaginatedData] = useState([])

  const handleTemplateClick = (templateId) => {
    navigate(`/data-migration?plan_id=${templateId}`)
  }

  useEffect(() => {
    fetchTemplates()
  }, [])

  const fetchTemplates = async () => {
    try {
      setLoading(true)
      const response = await getMigrationPlans(email)

      setRawApiResponse(response)
      let templatesData = []

      if (response) {
        if (Array.isArray(response)) {
          templatesData = response
        } else if (response.data && Array.isArray(response.data)) {
          templatesData = response.data
        } else if (typeof response === "object") {
          const possibleArrays = Object.values(response).filter((val) => Array.isArray(val))
          if (possibleArrays.length > 0) {
            templatesData = possibleArrays[0]
          }
        }
      }

      if (templatesData.length > 0) {
        const formattedData = templatesData.map((item) => (
          if(item.migrationsCount > 0){
            setTemplateUsedCount(templateUsedCount + 1)
          }
          return(

            {
              id: item.id || item.templateId || "",
              planId: item.plan_id || item.id || "",
          name: item.plan_name || item.name || item.templateName || `Template ${item.id || item.templateId || ""}`,
          source:
            item.migration_source && item.migration_source.length > 0 && item.migration_source[0].name
              ? item.migration_source[0].name
              : item.source || item.sourceType || "Source",
              target:
              item.migration_target && item.migration_target.length > 0 && item.migration_target[0].name
              ? item.migration_target[0].name
              : item.target || item.targetType || "Target",
              createdOn: item.createdAt || item.timestamp || "12.03.2025",
              metadata: item.metadata || {},
              updatedAt: item.updatedAt || item.createdAt || item.timestamp || "12.03.2025",
              migrationsCount: item.migrationsCount || item.usageCount || "0",
              editHistory: item.editHistory ||
              item.history || [
                "Edited on- Date, time stamp",
              "Edited on- Date, time stamp",
              "Edited on- Date, time stamp",
            ],
          }))
        )

        // Sort templates by updatedAt (most recent first)
        formattedData.sort((a, b) => {
          const dateA = new Date(a.updatedAt || a.createdOn)
          const dateB = new Date(b.updatedAt || b.createdOn)
          return dateB - dateA
        })

        setTemplates(formattedData)
        setFilteredTemplates(formattedData)
      } else {
        console.log("No templates data found in response:", response)
        setTemplates([])
        setFilteredTemplates([])
      }
    } catch (err) {
      console.error("Error fetching templates:", err)
      setError("Failed to load templates data")
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    let filtered = templates

    if (filterType !== "all") {
      if (filterType === "used") {
        filtered = filtered.filter(
          (template) => template.migrationsCount && Number.parseInt(template.migrationsCount) > 0,
        )
      } else if (filterType === "unused") {
        filtered = filtered.filter(
          (template) => !template.migrationsCount || Number.parseInt(template.migrationsCount) === 0,
        )
      } else if (filterType === "bySource" && selectedSourcePlatform) {
        filtered = filtered.filter(
          (template) => template.source.toLowerCase() === selectedSourcePlatform.props.children.toLowerCase(),
        )
      } else if (filterType === "byTarget" && selectedTargetPlatform) {
        filtered = filtered.filter(
          (template) => template.target.toLowerCase() === selectedTargetPlatform.props.children.toLowerCase(),
        )
      }
    }

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter((template) => template.name.toLowerCase().includes(searchTerm.toLowerCase()))
    }

    setFilteredTemplates(filtered)
    setCurrentPage(0) // Reset to first page when filtering
  }, [searchTerm, templates, filterType, selectedSourcePlatform, selectedTargetPlatform])

  // Toggle main filter dropdown
  const toggleFilterDropdown = () => {
    setShowFilterDropdown(!showFilterDropdown)
    // Close other dropdowns
    setShowSourcePlatformDropdown(false)
    setShowTargetPlatformDropdown(false)
  }

  // Toggle source platform dropdown
  const toggleSourcePlatformDropdown = () => {
    setShowSourcePlatformDropdown(!showSourcePlatformDropdown)
    // Close other dropdowns
    setShowFilterDropdown(false)
    setShowTargetPlatformDropdown(false)
  }

  // Toggle target platform dropdown
  const toggleTargetPlatformDropdown = () => {
    setShowTargetPlatformDropdown(!showTargetPlatformDropdown)
    // Close other dropdowns
    setShowFilterDropdown(false)
    setShowSourcePlatformDropdown(false)
  }

  // Handle filter selection
  const handleFilterSelect = (filterValue) => {
    setFilterType(filterValue)
    setShowFilterDropdown(false)

    // Reset platform selections when changing filter type
    if (filterValue !== "bySource") {
      setSelectedSourcePlatform(null)
    }

    if (filterValue !== "byTarget") {
      setSelectedTargetPlatform(null)
    }
  }

  // Handle source platform selection
  const handleSourcePlatformSelect = (platform) => {
    setSelectedSourcePlatform(platform)
    setShowSourcePlatformDropdown(false)

    // Automatically set filter type to "bySource" when a source platform is selected
    setFilterType("bySource")
  }

  // Handle target platform selection
  const handleTargetPlatformSelect = (platform) => {
    setSelectedTargetPlatform(platform)
    setShowTargetPlatformDropdown(false)

    // Automatically set filter type to "byTarget" when a target platform is selected
    setFilterType("byTarget")
  }

  // Get display text for the current filter
  const getFilterDisplayText = () => {
    switch (filterType) {
      case "used":
        return "Used for migration"
      case "unused":
        return "Not used for migration"
      case "bySource":
        return selectedSourcePlatform ? `By source: ${selectedSourcePlatform.props.children}` : "By source platform"
      case "byTarget":
        return selectedTargetPlatform ? `By target: ${selectedTargetPlatform.props.children}` : "By target platform"
      default:
        return "See all"
    }
  }

  // Update paginated data
  useEffect(() => {
    const startIndex = currentPage * rowsPerPage
    const endIndex = startIndex + rowsPerPage
    setPaginatedData(filteredTemplates.slice(startIndex, endIndex))
  }, [filteredTemplates, currentPage, rowsPerPage])

  // Calculate total pages
  const totalPages = Math.ceil(filteredTemplates.length / rowsPerPage)

  // Handle page change
  const handlePageChange = (pageNumber) => {
    if (pageNumber >= 0 && pageNumber < totalPages) {
      setCurrentPage(pageNumber)
    }
  }

  // Handle rows per page change
  const handleRowsPerPageChange = (rows) => {
    setRowsPerPage(rows)
    setCurrentPage(0)
  }

  const handleDeleteClick = (template, e) => {
    e.stopPropagation() // Prevent the row click event from triggering
    setTemplateToDelete(template)
    setShowDeleteModal(true)
  }

  const handleConfirmDelete = async () => {
    if (!templateToDelete?.planId) {
      console.error("No planId found for deletion")
      return
    }

    try {
      setDeleteLoading(true)
      const response = await deleteplan(templateToDelete.planId)
      console.log(response)

      if (response) {
        setTemplates((prev) => prev.filter((t) => t.planId !== templateToDelete.planId))
        setShowDeleteModal(false)
        setTemplateToDelete(null)
      } else {
        console.error("Failed to delete template:", response)
        setError("Failed to delete template")
      }
    } catch (err) {
      console.error("Error deleting template:", err)
      setError("Error deleting template")
    } finally {
      setDeleteLoading(false)
    }
  }

  // Handle close delete modal
  const handleCloseDeleteModal = () => {
    setShowDeleteModal(false)
    setTemplateToDelete(null)
  }

  // Toggle timeline dropdown
  const toggleTimeline = (templateId, e) => {
    e.stopPropagation() // Prevent the row click event from triggering
    setExpandedTimelines((prev) => ({
      ...prev,
      [templateId]: !prev[templateId],
    }))
  }

  // Calculate stats
  const stats = {
    created: templates?.length,
    used: templates?.filter((t) => t.migrationsCount && Number.parseInt(t.migrationsCount) > 0).length,
    unused: templates?.filter((t) => !t.migrationsCount || Number.parseInt(t.migrationsCount) === 0).length,
  }

  // Format timestamp to "Nov 6, 2024, 04:49 PM" format
  const formatTimestamp = (timestamp) => {
    if (!timestamp) return "N/A"

    try {
      // Convert to Date object
      const date = new Date(timestamp)

      // Format the date
      const options = {
        year: "numeric",
        month: "short",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
        hour12: true,
      }

      return date.toLocaleDateString("en-US", options)
    } catch (error) {
      console.error("Error formatting timestamp:", error)
      return "Invalid date"
    }
  }

  // Get visible page numbers with ellipsis
  const getVisiblePages = () => {
    const visiblePages = []
    const maxVisiblePages = 5

    if (totalPages <= maxVisiblePages) {
      return Array.from({ length: totalPages }, (_, i) => i)
    }

    let startPage = Math.max(0, currentPage - Math.floor(maxVisiblePages / 2))
    const endPage = Math.min(totalPages - 1, startPage + maxVisiblePages - 1)

    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(0, endPage - maxVisiblePages + 1)
    }

    if (startPage > 0) {
      visiblePages.push(0)
      if (startPage > 1) {
        visiblePages.push("...")
      }
    }

    for (let i = startPage; i <= endPage; i++) {
      visiblePages.push(i)
    }

    if (endPage < totalPages - 1) {
      if (endPage < totalPages - 2) {
        visiblePages.push("...")
      }
      visiblePages.push(totalPages - 1)
    }    return visiblePages
  }
  return loading ? (
    <LoaderSpinner />
  ) : (
    <div className={styles.container} style={{ overflow: 'hidden' }}>
      <Sidebar isCollapsed={isSidebarCollapsed} setIsCollapsed={setIsSidebarCollapsed} />
      
      <div className={`${styles.mainSection} ${isSidebarCollapsed ? styles.expanded : ""}`} style={{ overflowY: 'auto', overflowX: 'hidden' }}>
        <div className={styles.headerContainer}>          <h1 className={globalStyles.headerStyle} style={{ paddingLeft: "0" }}>Saved Templates</h1>
          <div className={styles.headerControls}>
            {/* Search input commented out 
            <div className={globalStyles.searchWrapper}>
              <SearchIcon className={globalStyles.searchIcon} />
              <input
                type="text"
                placeholder="Search..."
                className={globalStyles.searchInput}
                onFocus={(e) => {
                  e.target.style.width = "200px"
                  e.target.placeholder = "Typing..."
                }}
                onBlur={(e) => {
                  e.target.style.width = "80px"
                  e.target.placeholder = "Search..."
                }}
              />
            </div>
            */}

            <div className={globalStyles.searchWrapper} style={{ marginRight: "0" }}>
              <GlobeIcon className={globalStyles.searchIcon} />
              <input
                type="text"
                placeholder="Eng"
                className={`${globalStyles.searchInput} ${globalStyles.languageSelector}`}
                readOnly
              />
            </div>
          </div>
        </div>

        <div className={styles.statsContainer}>
          <div className={styles.statItem}>
            <div className={styles.statContent}>
              <span className={styles.statNumber}>{templates.length > 0 ? stats.created : "00"}</span>
              <span className={styles.statLabel}>Templates created</span>
            </div>
          </div>
          <div className={styles.statItem}>
            <div className={styles.statContent}>
              <span className={styles.statNumber}>{templates.length > 0 ? stats.used : "00"}</span>
              <span className={styles.statLabel}>Templates used for migration</span>
            </div>
          </div>
          <div className={styles.statItem}>
            <div className={styles.statContent}>
              <span className={styles.statNumber}>{templates.length > 0 ? stats.unused : "00"}</span>
              <span className={styles.statLabel}>Unused templates</span>
            </div>
          </div>
        </div>

        {/* Modified List of Templates section */}
        <div
          className={styles.listOfTemplates}
          style={{
            backgroundColor: templates.length === 0 ? "#DCDAD9" : "#170903",
            color: templates.length === 0 ? "#170903" : "#ea5822",
            justifyContent: templates.length === 0 ? "center" : "space-between",
          }}
        >
          {templates.length === 0 ? (
            <div className={globalStyles.guideName}>
              <div style={{ fontSize: "16px" }}>Yet to create a template</div>
            </div>
          ) : (
            <div className={globalStyles.selectionName}>
              <div>LIST OF TEMPLATES</div>
            </div>
          )}
        </div>

        {templates.length === 0 && !loading ? (
          <div></div>
        ) : (
          <>
            <div className={styles.tableControls}>
              <div className={styles.filterContainer}>
                {/* Main Filter Button */}
                <div className={styles.filterDropdownContainer}>
                  <button className={styles.filterButton} onClick={toggleFilterDropdown}>
                    <HiOutlineFunnel />
                    {getFilterDisplayText()}
                    <span className={`${styles.chevron} ${showFilterDropdown ? styles.chevronUp : ""}`}>
                      <ChevronDownIcon className={styles.chevronIcon} />
                    </span>
                  </button>

                  {showFilterDropdown && (
                    <div className={styles.filterDropdown}>
                      <div className={styles.filterOption} onClick={() => handleFilterSelect("all")}>
                        See all
                      </div>
                      <div className={styles.filterOption} onClick={() => handleFilterSelect("used")}>
                        Used for migration
                      </div>
                      <div className={styles.filterOption} onClick={() => handleFilterSelect("unused")}>
                        Not used for migration
                      </div>
                      <div className={styles.filterOption} onClick={() => handleFilterSelect("bySource")}>
                        By source platform
                      </div>
                      <div className={styles.filterOption} onClick={() => handleFilterSelect("byTarget")}>
                        By target platform
                      </div>
                    </div>
                  )}
                </div>

                {/* Source Platform Selector - Visible when "By source platform" is selected */}
                {filterType === "bySource" && (
                  <div className={styles.filterDropdownContainer}>
                    <button className={styles.platformSelectorButton} onClick={toggleSourcePlatformDropdown}>
                      <HiOutlineFunnel />
                      {selectedSourcePlatform ? selectedSourcePlatform.props.children : "Select source platform"}
                      <span className={`${styles.chevron} ${showSourcePlatformDropdown ? styles.chevronUp : ""}`}>
                        <ChevronDownIcon className={styles.chevronIcon} />
                      </span>
                    </button>

                    {showSourcePlatformDropdown && (
                      <div className={styles.platformDropdown}>
                        {sourcePlatforms.map((platform, index) => (
                          <div
                            key={index}
                            className={styles.platformOption}
                            onClick={() => handleSourcePlatformSelect(platform)}
                          >
                            {platform}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}

                {/* Target Platform Selector - Visible when "By target platform" is selected */}
                {filterType === "byTarget" && (
                  <div className={styles.filterDropdownContainer}>
                    <button className={styles.platformSelectorButton} onClick={toggleTargetPlatformDropdown}>
                      <HiOutlineFunnel />
                      {selectedTargetPlatform ? selectedTargetPlatform.props.children : "Select target platform"}
                      <span className={`${styles.chevron} ${showTargetPlatformDropdown ? styles.chevronUp : ""}`}>
                        <ChevronDownIcon className={styles.chevronIcon} />
                      </span>
                    </button>

                    {showTargetPlatformDropdown && (
                      <div className={styles.platformDropdown}>
                        {targetPlatforms.map((platform, index) => (
                          <div
                            key={index}
                            className={styles.platformOption}
                            onClick={() => handleTargetPlatformSelect(platform)}
                          >
                            {platform}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}

                <div className={globalStyles.searchWrapper}style={{ marginRight: 0 }}>
                  <SearchIcon className={globalStyles.searchIcon} />
                  <input
                    type="text"
                    placeholder="Search template name"
                    className={globalStyles.searchInput}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    onFocus={(e) => {
                      e.target.style.width = "200px"
                      e.target.placeholder = "Typing template name..."
                    }}
                    onBlur={(e) => {
                      e.target.style.width = "80px"
                      e.target.placeholder = "Search template name"
                    }}
                  />
                </div>
              </div>
            </div>

            {loading ? (
              <div className={styles.loadingContainer}>Loading templates...</div>
            ) : error ? (
              <div className={styles.errorContainer}>{error}</div>
            ) : (
              <div className={styles.tableWrapper}>
                <table className={styles.templatesTable}>
                  <thead>
                    <tr>
                      <th>Template name</th>
                      <th>Source to Target</th>
                      <th>Timeline</th>
                      <th>No.of Migrations</th>
                      <th>Delete</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredTemplates.length > 0 ? (
                      paginatedData.map((template, index) => (
                        <tr
                          key={template.id || index}
                          className={styles.rowSelect}
                          onClick={() => handleTemplateClick(template.id)}
                        >
                          <td>
                            <a href={`/data-migration?planId=${template.id}`} className={styles.templateLink}>
                              {template.name}
                            </a>
                          </td>
                          <td>
                            <div className={styles.sourceTarget}>
                              <span>{template.source}</span>
                              <span className={styles.arrow}>→</span>
                              <span>{template.target}</span>
                            </div>
                          </td>
                          <td>
                            <div className={styles.timeline}>
                              <div className={styles.timelineHeader}>
                                <div className={styles.timelineItem}>
                                  Created on: {formatTimestamp(template.createdOn)}
                                </div>
                                <div className={styles.timelineItem}>
                                  Updated at: {formatTimestamp(template.updatedAt)}
                                </div>
                              </div>
                            </div>
                          </td>
                          <td>{template?.metadata?.totalMigrationCount || "0"}</td>
                          <td>
                            <button className={styles.deleteButton} onClick={(e) => handleDeleteClick(template, e)}>
                              <img src="/assets/minus-sign.png" alt="Delete" className={styles.deleteIcon} />
                            </button>
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td colSpan="5">
                          <div className={styles.noDataMessage}>No Plans Available</div>
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            )}

            {filteredTemplates.length > 0 && (
              <div className={styles.paginationContainer}>
                <div className={styles.pageInfo}>
                  <span className={styles.paginationText}>Page no</span>
                  <div className={styles.pageButtons}>
                    <button
                      className={styles.pageNavButton}
                      onClick={() => handlePageChange(0)}
                      disabled={currentPage === 0}
                    >
                      «
                    </button>
                    <button
                      className={styles.pageNavButton}
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage === 0}
                    >
                      ‹
                    </button>

                    {getVisiblePages().map((page, index) =>
                      page === "..." ? (
                        <span key={`ellipsis-${index}`} className={styles.pageEllipsis}>
                          ...
                        </span>
                      ) : (
                        <button
                          key={page}
                          className={`${styles.pageButton} ${currentPage === page ? styles.active : ""}`}
                          onClick={() => handlePageChange(page)}
                        >
                          {(page + 1).toString().padStart(2, "0")}
                        </button>
                      ),
                    )}

                    <button
                      className={styles.pageNavButton}
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={currentPage >= totalPages - 1}
                    >
                      ›
                    </button>
                    <button
                      className={styles.pageNavButton}
                      onClick={() => handlePageChange(totalPages - 1)}
                      disabled={currentPage >= totalPages - 1}
                    >
                      »
                    </button>
                  </div>
                </div>
                <div className={styles.rowsPerPageContainer}>
                  <span className={styles.paginationText}>Show</span>
                  <div className={styles.rowsButtons}>
                    <button
                      className={`${styles.rowsButton} ${rowsPerPage === 25 ? styles.active : ""}`}
                      onClick={() => handleRowsPerPageChange(25)}
                    >
                      25
                    </button>
                    <button
                      className={`${styles.rowsButton} ${rowsPerPage === 50 ? styles.active : ""}`}
                      onClick={() => handleRowsPerPageChange(50)}
                    >
                      50
                    </button>
                    <button
                      className={`${styles.rowsButton} ${rowsPerPage === 75 ? styles.active : ""}`}
                      onClick={() => handleRowsPerPageChange(75)}
                    >
                      75
                    </button>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </div>

      {showDeleteModal && templateToDelete && (
        <div className={styles.modalOverlay}>
          <div className={styles.deleteModal}>
            <div className={styles.deleteModalHeader}>
              <h3>DO YOU WANT TO DELETE {templateToDelete.name.toUpperCase()}</h3>
              <button className={styles.closeModalButton} onClick={handleCloseDeleteModal}>
                ✕
              </button>
            </div>
            <div className={styles.deleteModalContent}>
              <p>Deleting this template is permanent.</p>
              <p>
                All associated mappings, API keys, and configuration details will be removed and cannot be recovered. Do
                you want to proceed?
              </p>
              <button className={styles.deleteConfirmButton} onClick={handleConfirmDelete} disabled={deleteLoading}>
                {deleteLoading ? "Deleting..." : "Delete permanently"}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default SavedTemplates
