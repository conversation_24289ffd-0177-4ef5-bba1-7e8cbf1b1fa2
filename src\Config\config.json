{"INTERCOM_APP_ID": "eahbkmrv", "INTERCOM_KEY": "GnYKel591aNof8fpZ1mohm4WnTI64LuZ-LnzWs5k", "BUSINESS_LOGIC_API_URL": "https://api.saasgenie.ai", "DB_WRAPPER_API_URL": "https://db-wrapper-dhbxaqhufhcmbmce.canadacentral-01.azurewebsites.net", "CONNECTORS_LIST": ["See all", "Zendesk", "Freshservice", "CSV", "Hubspot", "Deskpro", "Freshdesk", "ServiceNow", "SymphonyAI - Apex", "Jira Service Management", "Salesforce", "Easy vista", "Intercom", "Manage Engine", "SolarWinds"], "INTERCOM_ARTICLES_ENUM": [{"id": "********", "title": "What happens once my account is deleted?"}, {"id": "********", "title": "How does migrateGenie protect my files and API keys?"}, {"id": "********", "title": "Can I change or upgrade my plan after payment?"}, {"id": "********", "title": "Can I get discounts for bulk migrations?"}, {"id": "********", "title": "Vidéos d'introduction à Omnimed"}, {"id": "********", "title": "Visuel d'impression de la note clinique"}, {"id": "********", "title": "Survol de la boîte sommaire Médications"}, {"id": "********", "title": "Masquer le menu de gauche"}, {"id": "********", "title": "Intégrer votre système de préfacturation avec Omnimed"}, {"id": "********", "title": "Comprendre le fonctionnement de l'aviseur thérapeutique"}, {"id": "********", "title": "Visualiser et modifier des outils ou formulaires préalablement complétés"}, {"id": "********", "title": "Afficher plusieurs agendas en simultané (multi-intervenant et multisite)"}, {"id": "********", "title": "Renouveler une prescription"}, {"id": "********", "title": "Consulter les rapports d'imagerie médicale au DSQ"}, {"id": "11385399", "title": "Vidéos d'introduction à Omnimed"}, {"id": "11385396", "title": "Visuel d'impression de la note clinique"}, {"id": "11385395", "title": "Survol de la boîte sommaire Médications"}, {"id": "11385393", "title": "Masquer le menu de gauche"}, {"id": "11385392", "title": "Intégrer votre système de préfacturation avec Omnimed"}, {"id": "11385390", "title": "Comprendre le fonctionnement de l'aviseur thérapeutique"}, {"id": "11385388", "title": "Visualiser et modifier des outils ou formulaires préalablement complétés"}, {"id": "11385387", "title": "Afficher plusieurs agendas en simultané (multi-intervenant et multisite)"}, {"id": "11385383", "title": "Renouveler une prescription"}, {"id": "11385381", "title": "Consulter les rapports d'imagerie médicale au DSQ"}, {"id": "11385368", "title": "Vidéos d'introduction à Omnimed"}, {"id": "11385366", "title": "Visuel d'impression de la note clinique"}, {"id": "11385365", "title": "Survol de la boîte sommaire Médications"}, {"id": "11385362", "title": "Masquer le menu de gauche"}, {"id": "11385360", "title": "Intégrer votre système de préfacturation avec Omnimed"}, {"id": "11385358", "title": "Comprendre le fonctionnement de l'aviseur thérapeutique"}, {"id": "11385356", "title": "Visualiser et modifier des outils ou formulaires préalablement complétés"}, {"id": "11385354", "title": "Afficher plusieurs agendas en simultané (multi-intervenant et multisite)"}, {"id": "11385353", "title": "Renouveler une prescription"}, {"id": "11385351", "title": "Consulter les rapports d'imagerie médicale au DSQ"}, {"id": "11385332", "title": "Si vous n'avez pas de clé DSQ"}, {"id": "11332882", "title": "Vidéos d'introduction à Omnimed"}, {"id": "11332881", "title": "Visuel d'impression de la note clinique"}, {"id": "11332880", "title": "Survol de la boîte sommaire Médications"}, {"id": "11332879", "title": "Masquer le menu de gauche"}, {"id": "11332878", "title": "Intégrer votre système de préfacturation avec Omnimed"}, {"id": "11332877", "title": "Comprendre le fonctionnement de l'aviseur thérapeutique"}, {"id": "11332876", "title": "Visualiser et modifier des outils ou formulaires préalablement complétés"}, {"id": "11332875", "title": "Afficher plusieurs agendas en simultané (multi-intervenant et multisite)"}, {"id": "11332873", "title": "Renouveler une prescription"}, {"id": "11332872", "title": "Consulter les rapports d'imagerie médicale au DSQ"}, {"id": "11330430", "title": "Testing callouts in API created articles"}, {"id": "11330418", "title": "Testing callouts in API created articles"}, {"id": "11330416", "title": "Testing callouts in API created articles"}, {"id": "11330412", "title": "Testing callouts in API created articles"}, {"id": "11330390", "title": "Testing callouts in API created articles"}, {"id": "11330387", "title": "Testing callouts in API created articles"}, {"id": "11330386", "title": "Testing callouts in API created articles"}, {"id": "11330379", "title": "Testing callouts in API created articles"}, {"id": "11330377", "title": "Testing callouts in API created articles"}, {"id": "11330324", "title": "Testing callouts in API created articles"}, {"id": "11330320", "title": "Testing callouts in API created articles"}, {"id": "11330316", "title": "Testing callouts in API created articles"}, {"id": "11330314", "title": "Testing callouts in API created articles"}, {"id": "11330302", "title": "Testing callouts in API created articles"}, {"id": "11330277", "title": "Testing callouts in API created articles"}, {"id": "11330216", "title": "Testing callouts in API created articles"}, {"id": "11328262", "title": "What is include?"}, {"id": "11328257", "title": "What is a query?"}, {"id": "11328254", "title": "What is page[after]?"}, {"id": "11328248", "title": "What is page[size]?"}, {"id": "11328244", "title": "What is a Zendesk Instance URL?"}, {"id": "11328233", "title": "What is an offset?"}, {"id": "11328228", "title": "What are filters?"}, {"id": "11328226", "title": "What is a limit?"}, {"id": "11328223", "title": "What are query parameters?"}, {"id": "11328218", "title": "What is a ServiceNow Instance URL?"}, {"id": "11328209", "title": "How to retry failed records without redoing the migration?"}, {"id": "11328198", "title": "How to fix migration errors?"}, {"id": "11328195", "title": "What is a tag name?"}, {"id": "11328191", "title": "What are conversations?"}, {"id": "11328185", "title": "What are value mapping options?"}, {"id": "11328178", "title": "What are tickets?"}, {"id": "11328171", "title": "What are requesters?"}, {"id": "11328162", "title": "What are departments?"}, {"id": "11328156", "title": "What are agents?"}, {"id": "11328131", "title": "What are groups?"}, {"id": "11328127", "title": "How do I fix errors and mismatched data?"}, {"id": "11409336", "title": "Why do all these tabs appear?"}, {"id": "11328117", "title": "What are records?"}, {"id": "11328115", "title": "What are objects?"}, {"id": "11328115", "title": "What are objects?"}, {"id": "11328112", "title": "What is a workspace?"}, {"id": "11328107", "title": "How do I find my instance URL & Freshservice API key?"}, {"id": "11328104", "title": "What target platforms are supported?"}, {"id": "11328098", "title": "What source platforms are supported?"}, {"id": "11164231", "title": "Confirm the patient's appointment by email or SMS"}, {"id": "11156799", "title": "Untitled public article"}, {"id": "11156797", "title": "Untitled public article"}, {"id": "11110134", "title": "Online appointment booking and appointment request - Configure your online services"}, {"id": "11110133", "title": "Activate patient portal - appointment requests, online appointments and patient forms"}, {"id": "11110132", "title": "Appointment request - Where to start?"}, {"id": "11110131", "title": "Appointment request - View and process a request"}, {"id": "11110130", "title": "Send an invoice by email"}, {"id": "11110129", "title": "Clinical tools list"}, {"id": "11110128", "title": "Confirm the patient's appointment by email or SMS"}, {"id": "11110127", "title": "Checklist for administrative staff • Getting started with Omnimed"}, {"id": "11110126", "title": "Propose an appointment by email or SMS"}, {"id": "11110125", "title": "Basic notions and concepts in order to fully understand the nature of Omnimed's electronic medical record"}, {"id": "11110124", "title": "Produce an invoice and record the payment"}, {"id": "11110123", "title": "Access the private billing module"}, {"id": "11110122", "title": "Private billing management module ( add, modify or delete a product, group or company )"}, {"id": "11110121", "title": "Send an email or SMS from the Administrative center"}, {"id": "********", "title": "Consult an appointment, event or time slot history"}, {"id": "********", "title": "Checklist for healthcare professionals • Getting started with Omnimed"}, {"id": "********", "title": "Send an email related to a result"}, {"id": "********", "title": "Manage your clinical tools preferences"}, {"id": "********", "title": "Add a clinical tool or form to a note"}, {"id": "********", "title": "Add an institution to your Omnimed account"}, {"id": "********", "title": "A single patient record, what does that mean?"}, {"id": "********", "title": "Display several agendas simultaneously (multi-professional and multi-site)"}, {"id": "********", "title": "Are you logged in the right institution?"}, {"id": "********", "title": "Retrieve the current clinical note"}, {"id": "********", "title": "Send the CNESST forms electronically"}, {"id": "********", "title": "Increase of the connection duration to the QHR"}, {"id": "********", "title": "Send a clinical tool by a secure email"}, {"id": "********", "title": "Patient records merger explained"}, {"id": "********", "title": "Update the contact information associated with your Omnimed account from your user profile"}, {"id": "********", "title": "Task queues management"}, {"id": "********", "title": "Visualize and modify an appointment"}, {"id": "********", "title": "Billable services policy"}, {"id": "********", "title": "Scanned signature setup"}, {"id": "********", "title": "Audit reports on record accesses and consents"}, {"id": "********", "title": "Installation procedure - For authorized service center and network administrator - QHR access"}, {"id": "********", "title": "Manage video call"}, {"id": "********", "title": "Register a patient with the RAMQ from the Administrative center"}, {"id": "********", "title": "Using schedule templates"}, {"id": "********", "title": "Add or modify a relationship to the patient record"}, {"id": "********", "title": "Prerequisites to access the QHR from the EMR"}, {"id": "********", "title": "Access the content of the patient record with his consent"}, {"id": "********", "title": "The RAMQ import of a patient is not done in Omnimed, why?"}, {"id": "11110092", "title": "Assigning a status to an appointment"}, {"id": "11110091", "title": "Supplying your prescription orders to the QHR"}, {"id": "11110090", "title": "Complete an order"}, {"id": "11110089", "title": "Cancel a time slot"}, {"id": "11110088", "title": "Tasks module"}, {"id": "11110087", "title": "Subscription to receive electronic results"}, {"id": "11110086", "title": "Add Omnimed to your bookmarks"}, {"id": "11110085", "title": "Adding the QHR key identifier to your user profile"}, {"id": "11110084", "title": "Electronic Consultations - List of specialties per region"}, {"id": "11110083", "title": "Security question and password reset"}, {"id": "11110082", "title": "Fax a note"}, {"id": "11110080", "title": "Fax a clinical tool"}, {"id": "11110079", "title": "Navigate in a calendar"}, {"id": "11110078", "title": "Signing electronically with the electronic signature"}, {"id": "11110077", "title": "Set up your user profile for a better experience"}, {"id": "11110076", "title": "Impossible to access the QHR in Omnimed"}, {"id": "11110075", "title": "Administrative center: Summarized presentation"}, {"id": "11110074", "title": "Visualize and modify a schedule"}, {"id": "11110071", "title": "Fax a prescription"}, {"id": "11110069", "title": "Add files or pictures to the clinical note"}, {"id": "********", "title": "Clinical tools overview"}, {"id": "********", "title": "Management of perpetual file duplicates"}, {"id": "********", "title": "Create a new patient record"}, {"id": "********", "title": "Add a prescription to the order (4 options)"}, {"id": "********", "title": "Appointment statuses management module"}, {"id": "********", "title": "View the patient's laboratory results available in the QHR"}, {"id": "********", "title": "Access to Administration module (administrator account)"}, {"id": "********", "title": "Configuring RAMQ registration import"}, {"id": "********", "title": "Create and manage your list of favorite prescriptions"}, {"id": "********", "title": "How to update Google Chrome"}, {"id": "********", "title": "Appointment and Waiting room modules"}, {"id": "********", "title": "Print the current clinical note"}, {"id": "********", "title": "Clean up the Medications profile (archive, end, stop, delete and cancel)"}, {"id": "********", "title": "Attach prerequisites to the consultation request form for specialized services (APSS)"}, {"id": "********", "title": "Select your preferences in the Medications summary box"}, {"id": "********", "title": "Add confidential data to the note - confidential field"}, {"id": "********", "title": "Print a patient label, admin form or a request"}, {"id": "********", "title": "Add the reference record numbers of your patients"}, {"id": "********", "title": "System requirements for using Omnimed"}, {"id": "11110047", "title": "Filter the record's clinical notes list"}, {"id": "11110046", "title": "Modify the patient's personal information (name, HIN, gender, birth date, deceased date)"}, {"id": "11110045", "title": "RAMQ synchronization status"}, {"id": "11110044", "title": "Document the responsible healthcare workers of the patient"}, {"id": "11110043", "title": "Filter and sort the Tasks module"}, {"id": "11110042", "title": "Confirm fax delivery"}, {"id": "11110041", "title": "Quick tasks management"}, {"id": "11110040", "title": "Generate a report"}, {"id": "11110039", "title": "Create schedules"}, {"id": "11110038", "title": "Print past clinical notes"}, {"id": "11110037", "title": "Display a calendar / schedules"}, {"id": "11110036", "title": "Waiting rooms management"}, {"id": "11110035", "title": "Patient document printing management"}, {"id": "11110034", "title": "View the patient's appointments"}, {"id": "11110033", "title": "Target dose concept"}, {"id": "11110032", "title": "View and modify previously completed tools"}, {"id": "11110031", "title": "Get your institution unique id for the electronic transmission of the CRDS forms"}, {"id": "11110030", "title": "FAQ - Access the secure email sent to you"}, {"id": "11110028", "title": "Add notes about the patient and status"}, {"id": "11110027", "title": "Assigning appointments"}, {"id": "11110026", "title": "Patient search: Usage and features"}, {"id": "11110025", "title": "Terms of use"}, {"id": "11110024", "title": "Confirm the patient's presence or absence at an appointment"}, {"id": "11110023", "title": "Reassign appointments from canceled time slots or appointment proposal failures"}, {"id": "11110022", "title": "Working with quick tasks and favorite tasks"}, {"id": "11110021", "title": "Clinical note content"}, {"id": "11110020", "title": "Cancel or move an appointment"}, {"id": "11110018", "title": "Access the QHR viewer (imaging report, medications et laboratory results)"}, {"id": "11110017", "title": "Appointment activities management module"}, {"id": "11110016", "title": "Update the patient contact information"}, {"id": "11110015", "title": "Omnimed QHR viewer configuration"}, {"id": "11110014", "title": "Register a patient in a waiting room"}, {"id": "11110013", "title": "Select another mandate or institution"}, {"id": "11110012", "title": "Consult and update the Directory"}, {"id": "11110011", "title": "Delete a clinical tool"}, {"id": "11110010", "title": "Learning more about the current clinical note - Case study"}, {"id": "11110009", "title": "Rules of access to information and concept of mandates"}, {"id": "11110008", "title": "View the patient's medications available in the QHR"}, {"id": "11110007", "title": "Respect for the confidentiality of personal information"}, {"id": "11110006", "title": "Modify or delete a clinical note"}, {"id": "11109999", "title": "Interinstitutional tasks management"}, {"id": "11109998", "title": "What's an electronic consultation?"}, {"id": "11109991", "title": "Interinstitutional tasks management"}, {"id": "11109990", "title": "What's an electronic consultation?"}, {"id": "11109987", "title": "Interinstitutional tasks management"}, {"id": "11109986", "title": "What's an electronic consultation?"}, {"id": "11109983", "title": "Interinstitutional tasks management"}, {"id": "11109982", "title": "What's an electronic consultation?"}, {"id": "11109979", "title": "Interinstitutional tasks management"}, {"id": "11109978", "title": "What's an electronic consultation?"}, {"id": "11109969", "title": "Test Article"}, {"id": "10776130", "title": "Untitled public article"}, {"id": "10776083", "title": "Untitled public article"}, {"id": "10776063", "title": "Untitled public article"}, {"id": "10776062", "title": "Untitled public article"}, {"id": "10776056", "title": "Untitled public article"}, {"id": "10748725", "title": "Untitled public article"}, {"id": "10748717", "title": "Untitled public article"}, {"id": "10743512", "title": "Untitled public article"}, {"id": "10723882", "title": "Scribe public article test"}, {"id": "10625982", "title": "Untitled public article"}, {"id": "10552552", "title": "Test article 1"}, {"id": "10552541", "title": "Your first public article (Internal HelpCenter)"}, {"id": "10486890", "title": "Untitled public article"}, {"id": "10485725", "title": "Untitled public article"}, {"id": "10418419", "title": "Untitled public article"}, {"id": "10406841", "title": "Understanding the types of Tickets"}, {"id": "10363593", "title": "Untitled public article"}, {"id": "10363592", "title": "Untitled public article"}, {"id": "10363304", "title": "Untitled public article"}, {"id": "10275517", "title": "Untitled public article"}, {"id": "10246206", "title": "Welcome to your Help Center!"}, {"id": "10246205", "title": "Sample article: Stellar Skyonomy refund policies"}, {"id": "10246204", "title": "What are these sections and articles doing here?"}, {"id": "10246203", "title": "How do I customize my Help Center?"}, {"id": "10246202", "title": "How can agents leverage knowledge to help customers?"}, {"id": "10246201", "title": "How do I publish my content in other languages?"}, {"id": "10246200", "title": "Test Article (ZD)"}, {"id": "10246199", "title": "TEST #2"}, {"id": "10223384", "title": "Freshservice Migration - Important Notes"}, {"id": "10223314", "title": "Target Freshservice Post Migration Steps"}, {"id": "10223310", "title": "migrateGenie Results Dashboard"}, {"id": "10223305", "title": "Starting the Data Migration After the Mapping"}, {"id": "10223295", "title": "Freshservice Target Field Mapping"}, {"id": "10223291", "title": "Connecting to Freshservice as a Target"}, {"id": "10223289", "title": "Connecting to Zendesk as a Source"}, {"id": "10223287", "title": "Connecting to ServiceNow as a Source"}, {"id": "10223284", "title": "Connecting to CSV as a Source"}, {"id": "10223272", "title": "Creating a New Migration Plan"}, {"id": "10223269", "title": "Target Freshservice Pre-Migration Steps"}, {"id": "10219793", "title": "Migrating Data to Freshservice Using migrateGenie"}, {"id": "10208444", "title": "ServiceNow to Freshservice Migration Steps"}, {"id": "10207288", "title": "Supported Connectors"}, {"id": "10189381", "title": "CSV to Freshservice Migration Steps"}, {"id": "10077252", "title": "CSV File to Freshservice Data Preparation Steps"}, {"id": "9989928", "title": "Accelerators"}, {"id": "9989922", "title": "Customer Offerings"}, {"id": "9836525", "title": "What is migrate<PERSON>enie?"}, {"id": "9836517", "title": "About saasgenie"}], "TRUSTED_BY_LOGOS": [{"src": "/assets/Andrew Peller.png", "alt": "<PERSON>", "url": "https://www.saasgenie.ai/andrew-peller"}, {"src": "/assets/AGI.png", "alt": "AGI", "url": ""}, {"src": "/assets/AMS.png", "alt": "AMS", "url": ""}, {"src": "/assets/Seagate.png", "alt": "Seagate", "url": ""}, {"src": "/assets/CityOfKarratha.png", "alt": "City of Karratha", "url": ""}, {"src": "/assets/CCRLA.png", "alt": "CCRLA", "url": ""}, {"src": "/assets/New England Biolabs.png", "alt": "New England Biolabs", "url": ""}]}